<!-- File: frontend/src/views/asset/AssetAnalyticsWorkbench.vue -->
<!-- Description: 企业级智能资产分析工作台 - 交互式数据探索平台 -->

<template>
  <div class="analytics-workbench" :class="{ 'dark-theme': isDarkTheme, 'light-theme': !isDarkTheme }">
    <!-- 侧边控制面板 -->
    <aside class="control-panel">
      <div class="panel-header">
        <div class="logo-container">
          <el-icon class="logo-icon"><DataAnalysis /></el-icon>
          <h1 class="panel-title">资产分析工作台</h1>
        </div>
        <!-- 主题切换按钮 -->
        <el-button 
          type="text" 
          class="theme-toggle"
          @click="toggleTheme"
        >
          <el-icon v-if="isDarkTheme"><Sunny /></el-icon>
          <el-icon v-else><Moon /></el-icon>
        </el-button>
      </div>

      <div class="filters-panel">
        <!-- 分析维度选择 -->
        <div class="filter-group">
          <label class="filter-label">分析维度</label>
          <el-select
            v-model="queryParams.dimension"
            placeholder="选择分析维度"
            class="filter-select"
            @change="handleDimensionChange"
          >
            <el-option
              v-for="dim in availableDimensions"
              :key="dim.key"
              :label="dim.name"
              :value="dim.key"
            />
          </el-select>
        </div>

        <!-- 度量指标选择 -->
        <div class="filter-group">
          <label class="filter-label">度量指标</label>
          <el-select
            v-model="queryParams.metric"
            placeholder="选择度量指标"
            class="filter-select"
            @change="handleMetricChange"
          >
            <el-option
              v-for="metric in availableMetrics"
              :key="metric.key"
              :label="metric.name"
              :value="metric.key"
            />
          </el-select>
        </div>

        <!-- 资产状态筛选 -->
        <div class="filter-group">
          <label class="filter-label">资产状态</label>
          <div class="checkbox-group">
            <el-checkbox
              v-for="status in assetStatuses"
              :key="status"
              v-model="selectedStatuses"
              :label="status"
              @change="handleStatusChange"
            >
              {{ status }}
            </el-checkbox>
          </div>
        </div>

        <!-- 部门筛选 -->
        <div class="filter-group">
          <label class="filter-label">部门</label>
          <el-select
            v-model="queryParams.filters.department"
            placeholder="选择部门"
            class="filter-select"
            clearable
            @change="handleFilterChange"
          >
            <el-option label="所有部门" value="" />
            <el-option
              v-for="dept in departments"
              :key="dept"
              :label="dept"
              :value="dept"
            />
          </el-select>
        </div>

        <!-- 时间范围筛选 -->
        <div class="filter-group">
          <label class="filter-label">时间范围</label>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            class="filter-select"
            @change="handleDateRangeChange"
          />
        </div>

        <!-- 重置按钮 -->
        <el-button
          type="info"
          class="reset-btn"
          @click="resetFilters"
        >
          重置筛选
        </el-button>
      </div>
    </aside>

    <!-- 主内容区域 -->
    <main class="main-content">
      <!-- 页面头部 -->
      <header class="content-header">
        <h2 class="dashboard-title">{{ dashboardTitle }}</h2>
        <p class="dashboard-subtitle">{{ dashboardSubtitle }}</p>
      </header>

      <!-- KPI指标卡片 -->
      <section class="kpi-section">
        <div class="kpi-container">
          <div
            v-for="kpi in enhancedKpiMetrics"
            :key="kpi.label"
            class="kpi-card enhanced"
            :class="kpi.cardClass"
          >
            <div class="kpi-content">
              <div class="kpi-main">
                <p class="kpi-label">{{ kpi.label }}</p>
                <div class="kpi-value-container">
                  <h3 class="kpi-value" :class="kpi.valueClass">
                    {{ kpi.value }}
                    <span v-if="kpi.unit" class="kpi-unit">{{ kpi.unit }}</span>
                  </h3>
                  <div class="kpi-trend" v-if="kpi.trend !== undefined">
                    <el-icon class="trend-icon" :class="kpi.trend >= 0 ? 'trend-up' : 'trend-down'">
                      <ArrowUp v-if="kpi.trend >= 0" />
                      <ArrowDown v-else />
                    </el-icon>
                    <span class="trend-text">{{ Math.abs(kpi.trend).toFixed(1) }}%</span>
                  </div>
                </div>
              </div>
              <div class="kpi-chart" v-if="kpi.chartData">
                <div :ref="el => kpi.chartRef = el" class="mini-chart"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 数据调试信息 -->
      <section v-if="showDebugInfo" class="debug-section">
        <el-card class="debug-card">
          <template #header>
            <div class="debug-header">
              <h3 class="debug-title">数据调试信息</h3>
              <el-button type="warning" size="small" @click="showDebugInfo = false">关闭</el-button>
            </div>
          </template>
          <div class="debug-content">
            <h4>统计结果摘要:</h4>
            <pre>{{ JSON.stringify(statisticsResult?.summary || {}, null, 2) }}</pre>
            <h4>聚合数据 (前5项):</h4>
            <pre>{{ JSON.stringify(statisticsResult?.aggregatedData?.slice(0, 5) || [], null, 2) }}</pre>
            <h4>详细数据 (前2项):</h4>
            <pre>{{ JSON.stringify(tableData.slice(0, 2) || [], null, 2) }}</pre>
            <h4>表格列:</h4>
            <pre>{{ JSON.stringify(tableColumns || [], null, 2) }}</pre>
          </div>
        </el-card>
      </section>

      <!-- 主内容区 -->
      <div class="grid-layout">
        <!-- 左侧：资产分布与地图 -->
        <div class="left-column">
          <!-- 厂区地图容器 -->
          <el-card class="map-card">
            <template #header>
              <div class="card-header">
<h3>部门资产分布热力图</h3>
                <div class="header-actions">
                  <el-radio-group v-model="selectedArea" size="small" @change="handleAreaChange">
                    <el-radio-button value="all">全部部门</el-radio-button>
                    <el-radio-button 
                      v-for="dept in departmentOptions.slice(0, 4)" 
                      :key="dept.value" 
                      :value="dept.value">
                      {{ dept.label }}
                    </el-radio-button>
                  </el-radio-group>
                </div>
              </div>
            </template>
            <div class="factory-map-wide factory-map-compact">
              <div ref="factoryMap" class="factory-map factory-map-reduced"></div>
              <div class="map-legend">
                <div class="legend-title">在用资产密度</div>
                <div class="legend-gradient"></div>
                <div class="legend-labels">
                  <span>低</span>
                  <span>高</span>
                </div>
              </div>
              <div class="area-info" v-if="selectedAreaInfo || selectedArea === 'all'">
                <h4>{{ selectedArea === 'all' ? '全部部门统计' : selectedAreaInfo?.name }}</h4>
                <div class="area-stats">
                  <div class="stat-row">
                    <span class="stat-label">资产总数:</span>
                    <span class="stat-value">{{ getTotalAssetCount() }}台</span>
                  </div>
                  <div class="stat-row">
                    <span class="stat-label">在用资产:</span>
                    <span class="stat-value">{{ getTotalNormalCount() }}台</span>
                  </div>
                  <div class="stat-row">
                    <span class="stat-label">在用率:</span>
                    <span class="stat-value">{{ getTotalOnlineRate() }}%</span>
                  </div>
                  <div class="stat-row">
                    <span class="stat-label">故障设备:</span>
                    <span class="stat-value">{{ getTotalFaultCount() }}台</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>

        </div>

        <!-- 右侧：统计与图表 -->
        <div class="right-column">
          <!-- 资产类型饼图 -->
          <el-card class="asset-type-card">
            <template #header>
              <div class="chart-header">
                <h3 class="chart-title">资产类型</h3>
              </div>
            </template>
            <div class="asset-type-content asset-type-compact">
              <div ref="assetTypePieChart1" class="asset-type-pie-chart asset-type-reduced"></div>
              <div class="asset-type-legend">
                <div v-for="item in assetTypeData1" :key="item.name" class="legend-item">
                  <span class="legend-color" :style="{ backgroundColor: item.color }"></span>
                  <span class="legend-label">{{ item.name }}</span>
                  <span class="legend-value">{{ item.percentage }}%</span>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 资产状态环形图 -->
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <h3 class="chart-title">{{ chartTitle }}</h3>
                <div class="chart-actions">
                  <!-- 下钻控制按钮 -->
                  <div class="drill-controls" v-if="drillDownHistory.length > 0">
                    <el-button
                      type="info"
                      size="small"
                      @click="goBackToPreviousLevel"
                    >
                      <el-icon><ArrowLeft /></el-icon>
                      返回上级
                    </el-button>
                    <el-button
                      type="warning"
                      size="small"
                      @click="resetDrillDown"
                    >
                      <el-icon><Refresh /></el-icon>
                      重置下钻
                    </el-button>
                  </div>
                  
                  <!-- 图表类型切换 -->
                  <el-button-group>
                    <el-button
                      :type="chartType === 'bar' ? 'primary' : 'default'"
                      size="small"
                      @click="changeChartType('bar')"
                    >
                      柱状图
                    </el-button>
                    <el-button
                      :type="chartType === 'pie' ? 'primary' : 'default'"
                      size="small"
                      @click="changeChartType('pie')"
                    >
                      饼图
                    </el-button>
                    <el-button
                      :type="chartType === 'line' ? 'primary' : 'default'"
                      size="small"
                      @click="changeChartType('line')"
                    >
                      折线图
                    </el-button>
                  </el-button-group>
                </div>
              </div>
            </template>
            
            <div class="chart-container">
              <div
                ref="mainChart"
                class="chart-canvas"
                v-loading="chartLoading"
              ></div>
            </div>
          </el-card>
          
          <!-- 资产状态分布图 -->
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <h3 class="chart-title">资产状态分布</h3>
              </div>
            </template>
            <div class="chart-container">
              <div ref="statusDistributionChart" class="chart-canvas"></div>
            </div>
          </el-card>
        </div>
      </div>
      
      <!-- 第二行图表 -->
      <div class="grid-layout second-row">
        <!-- 资产价值分布图 -->
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <h3 class="chart-title">资产价值区间分布</h3>
            </div>
          </template>
          <div class="chart-container">
            <div ref="valueDistributionChart" class="chart-canvas"></div>
          </div>
        </el-card>
        
        <!-- 部门资产矩阵图 -->
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <h3 class="chart-title">部门资产矩阵</h3>
            </div>
          </template>
          <div class="chart-container">
            <div ref="matrixChart" class="chart-canvas"></div>
          </div>
        </el-card>
      </div>

      <!-- 数据表格区域 -->
      <section class="table-section">
        <el-card class="table-card">
          <template #header>
            <div class="table-header">
              <h3 class="table-title">数据明细</h3>
              <div class="table-summary">
                <span class="summary-text">显示 {{ statisticsResult?.summary?.totalRecords || 0 }} 条记录</span>
                <el-button type="info" size="small" @click="showDebugInfo = !showDebugInfo">
                  {{ showDebugInfo ? '隐藏调试' : '显示调试' }}
                </el-button>
                <el-button
                  type="primary"
                  size="small"
                  @click="exportData"
                >
                  导出数据
                </el-button>
              </div>
            </div>
          </template>

          <div v-if="!tableData.length && !tableLoading" class="empty-data">
            <el-empty description="暂无数据" />
          </div>

          <el-table
            v-else
            :data="tableData"
            v-loading="tableLoading"
            class="data-table"
            stripe
            border
            @row-click="handleRowClick"
          >
            <!-- 静态定义关键列 -->
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="assetCode" label="资产编号" width="120" />
            <el-table-column prop="assetName" label="资产名称" width="150" />
            <el-table-column prop="assetType" label="资产类型" />
            <el-table-column prop="department" label="部门" />
            <el-table-column prop="location" label="位置" />
            <el-table-column prop="status" label="状态" width="100" />
            <el-table-column 
              prop="price" 
              label="价值(万元)" 
              width="120"
              :formatter="(row) => row.price ? `¥${Number(row.price).toFixed(2)}万` : '-'"
            />
            <el-table-column 
              prop="createdAt" 
              label="创建时间"
              :formatter="(row) => row.createdAt ? new Date(row.createdAt).toLocaleDateString() : '-'"
            />
            <el-table-column prop="managerName" label="负责人" width="100" />
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-card>
      </section>
    </main>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { DataAnalysis, ArrowLeft, Refresh, Sunny, Moon, ArrowUp, ArrowDown, Clock } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { statisticsApi } from '@/api/statistics'
import { assetAnalyticsApi } from '@/api/asset-analytics'
import axios from 'axios'
import { 
  factoryAreas, 
  getHeatColor, 
  assetValueDistribution, 
  assetStatusDistribution,
  departmentAssetMatrix 
} from '@/assets/factory-layout.js'

// 响应式数据
const mainChart = ref(null)
const factoryMap = ref(null)
const valueDistributionChart = ref(null)
const matrixChart = ref(null)
const statusDistributionChart = ref(null)
const assetTypePieChart1 = ref(null)
const chartInstance = ref(null)
const factoryMapInstance = ref(null)
const valueDistributionInstance = ref(null)
const matrixInstance = ref(null)
const statusDistributionInstance = ref(null)
const pieChart1Instance = ref(null)
const chartLoading = ref(false)
const tableLoading = ref(false)
const chartType = ref('bar')
const showDebugInfo = ref(false)
const isDarkTheme = ref(true)
const selectedArea = ref('all')
const selectedAreaInfo = ref(null)

// 查询参数
const queryParams = reactive({
  dimension: 'department',
  metric: 'count',
  filters: {},
  dateRange: null,
  pagination: {
    page: 1,
    size: 20
  }
})

// 筛选条件
const selectedStatuses = ref(['在用', '闲置', '维修中', '报废'])
const dateRange = ref([])

// 可选项数据
const availableDimensions = ref([
  { key: 'department', name: '部门' },
  { key: 'location', name: '位置' },
  { key: 'type', name: '资产类型' },
  { key: 'status', name: '状态' }
])
const availableMetrics = ref([
  { key: 'count', name: '数量' },
  { key: 'totalValue', name: '总价值' },
  { key: 'averageValue', name: '平均价值' }
])
const assetStatuses = ref(['在用', '闲置', '维修中', '报废'])
const departments = ref(['技术部', '销售部', '市场部', '行政部', '财务部'])

// 统计结果数据
const statisticsResult = ref(null)
const workbenchData = ref(null)
const tableData = ref([])
const tableColumns = ref([])

// 下钻历史数据
const drillDownHistory = ref([])

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 计算属性
const dashboardTitle = computed(() => {
  if (queryParams.filters.department) {
    return `${queryParams.filters.department} - 资产洞察`
  }
  return '资产全局洞察'
})

const dashboardSubtitle = computed(() => {
  if (queryParams.filters.department) {
    return `当前已筛选"部门"为"${queryParams.filters.department}"`
  }
  return '当前展示所有资产的概览。您可以在左侧面板进行筛选和维度切换。'
})

const chartTitle = computed(() => {
  const dimensionName = availableDimensions.value.find(d => d.key === queryParams.dimension)?.name || '未知维度'
  const metricName = availableMetrics.value.find(m => m.key === queryParams.metric)?.name || '未知指标'
  return `按${dimensionName}分析 - ${metricName}`
})

const kpiMetrics = computed(() => {
  // 从工作台数据中获取KPI数据
  if (!workbenchData.value?.kpiData) return getDefaultKpiMetrics()
  
  const kpiData = workbenchData.value.kpiData
  
  return [
    {
      label: '资产总数',
      value: (kpiData.totalAssets || 0).toLocaleString(),
      valueClass: 'kpi-value-default'
    },
    {
      label: '资产总值',
      value: (kpiData.totalValue || 0).toFixed(2),
      unit: '万',
      valueClass: 'kpi-value-default'
    },
    {
      label: '在用率',
      value: `${(kpiData.onlineRate || 0).toFixed(1)}%`,
      valueClass: 'kpi-value-success'
    },
    {
      label: '维修中',
      value: kpiData.faultCount || 0,
      valueClass: 'kpi-value-warning'
    }
  ]
})

// 工作台统计数据（用于右侧统计卡片）
const workbenchStats = computed(() => {
  if (!workbenchData.value?.kpiData) {
    return {
      onlineRate: 0,
      idleRate: 0,
      maintenanceRate: 0,
      averageUtilization: 0
    }
  }
  
  const kpiData = workbenchData.value.kpiData
  return {
    onlineRate: (kpiData.onlineRate || 0).toFixed(1),
    idleRate: (kpiData.idleRate || 0).toFixed(1),
    maintenanceRate: (kpiData.maintenanceRate || 0).toFixed(1),
    averageUtilization: (kpiData.averageUtilization || 0).toFixed(1)
  }
})

// 增强的KPI指标（带折线图和趋势）
const enhancedKpiMetrics = computed(() => {
  if (!workbenchData.value?.kpiData) return getDefaultEnhancedKpiMetrics()
  
  const kpiData = workbenchData.value.kpiData
  
  return [
    {
      label: '总资产数',
      value: (kpiData.totalAssets || 0).toLocaleString(),
      valueClass: 'kpi-value-blue',
      cardClass: 'kpi-card-blue',
      trend: kpiData.totalTrend || 0,
      chartData: generateMiniChartData('line'),
      chartRef: null
    },
    {
      label: '故障数量',
      value: (kpiData.faultCount || 0).toLocaleString(),
      valueClass: 'kpi-value-green',
      cardClass: 'kpi-card-green',
      trend: kpiData.faultTrend || 0,
      chartData: generateMiniChartData('area'),
      chartRef: null
    },
    {
      label: '在线率',
      value: (kpiData.onlineRate || 0) + '%',
      valueClass: 'kpi-value-orange',
      cardClass: 'kpi-card-orange',
      trend: kpiData.onlineRateTrend || 0,
      chartData: generateMiniChartData('bar'),
      chartRef: null
    },
    {
      label: '维修中',
      value: (kpiData.maintenanceCount || 0).toLocaleString(),
      valueClass: 'kpi-value-purple',
      cardClass: 'kpi-card-purple',
      trend: kpiData.maintenanceTrend || 0,
      chartData: generateMiniChartData('line'),
      chartRef: null
    }
  ]
})

// 生成小图表数据
const generateMiniChartData = (type) => {
  const baseData = [120, 132, 101, 134, 90, 230, 210]
  switch (type) {
    case 'line':
      return baseData.map((val, i) => val + Math.sin(i) * 20)
    case 'area':
      return baseData.map((val, i) => val * 0.8 + Math.cos(i) * 15)
    case 'bar':
      return baseData.map((val, i) => val * 1.1 + (i % 2) * 10)
    case 'pie':
      return baseData.map((val, i) => val * 0.9 + Math.random() * 30)
    default:
      return baseData
  }
}

// 饼图数据（资产类型）
const assetTypeData1 = computed(() => {
  if (workbenchData.value?.typeStatistics && workbenchData.value.typeStatistics.length > 0) {
    console.log('使用真实资产类型数据:', workbenchData.value.typeStatistics)
    return workbenchData.value.typeStatistics.map((item, index) => ({
      name: item.assetTypeName,
      percentage: parseFloat(item.percentage),
      color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'][index % 5]
    }))
  }
  console.log('使用默认资产类型数据')
  return [
    { name: '计算机设备', percentage: 45.2, color: '#5470c6' },
    { name: '网络设备', percentage: 25.8, color: '#91cc75' },
    { name: '服务器', percentage: 15.3, color: '#fac858' },
    { name: '打印机', percentage: 8.9, color: '#ee6666' },
    { name: '其他设备', percentage: 4.8, color: '#73c0de' }
  ]
})

// 饼图数据（资产状态）
const assetTypeData2 = computed(() => {
  if (workbenchData.value?.statusDistribution) {
    return workbenchData.value.statusDistribution.statusLabels.map((label, index) => ({
      name: label,
      percentage: parseFloat(((workbenchData.value.statusDistribution.statusCounts[index] / workbenchData.value.statusDistribution.statusCounts.reduce((a, b) => a + b, 0)) * 100).toFixed(1)),
      color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'][index % 5]
    }))
  }
  return [
    { name: '在用', percentage: 78.5, color: '#5470c6' },
    { name: '闲置', percentage: 12.3, color: '#91cc75' },
    { name: '维修中', percentage: 6.8, color: '#fac858' },
    { name: '报废', percentage: 2.4, color: '#ee6666' }
  ]
})

// 默认增强KPI指标
const getDefaultEnhancedKpiMetrics = () => [
  {
    label: '总资产数',
    value: '0',
    valueClass: 'kpi-value-blue',
    cardClass: 'kpi-card-blue',
    trend: 0,
    chartData: generateMiniChartData('line'),
    chartRef: null
  },
  {
    label: '故障数量',
    value: '0',
    valueClass: 'kpi-value-green',
    cardClass: 'kpi-card-green',
    trend: 0,
    chartData: generateMiniChartData('area'),
    chartRef: null
  },
  {
    label: '在线率',
    value: '0%',
    valueClass: 'kpi-value-orange',
    cardClass: 'kpi-card-orange',
    trend: 0,
    chartData: generateMiniChartData('bar'),
    chartRef: null
  },
  {
    label: '维修中',
    value: '0',
    valueClass: 'kpi-value-purple',
    cardClass: 'kpi-card-purple',
    trend: 0,
    chartData: generateMiniChartData('line'),
    chartRef: null
  }
]

// 部门选项（从后端数据生成）
const departmentOptions = computed(() => {
  if (!workbenchData.value?.departmentStatistics) {
    console.log('部门选项：无数据')
    return []
  }
  
  const options = workbenchData.value.departmentStatistics.map(dept => ({
    value: dept.departmentId,
    label: dept.departmentName
  }))
  console.log('部门选项已生成:', options)
  return options
})

// 获取真实部门数据（从后端数据转换为厂区地图格式）
const getRealAreaData = () => {
  if (!workbenchData.value?.departmentStatistics) {
    console.log('部门统计数据不存在')
    return null
  }

  let deptStats = workbenchData.value.departmentStatistics
  if (!deptStats || deptStats.length === 0) {
    console.log('部门统计数据为空')
    return null
  }

  // 如果选择了特定部门，只显示该部门
  if (selectedArea.value && selectedArea.value !== 'all') {
    deptStats = deptStats.filter(dept => dept.departmentId === selectedArea.value)
    console.log('筛选特定部门:', selectedArea.value, deptStats)
  } else {
    console.log('显示所有部门数据，部门数量:', deptStats.length)
    // 确保第一屏显示所有部门的热力图数据
    console.log('第一屏热力图：显示全部', deptStats.length, '个部门的资产分布')
  }
  
  // 为每个部门生成优化的坐标布局（调大图块尺寸）
  const generateCoords = (index, total) => {
    // 根据部门数量动态调整布局，优化图块大小
    const cols = Math.min(Math.ceil(Math.sqrt(total)), 5) // 减少列数以增大图块
    const rows = Math.ceil(total / cols)
    const row = Math.floor(index / cols)
    const col = index % cols

    // 增大图块尺寸以便更好地显示热力数据（适应新的容器高度）
    const containerWidth = 580 // 容器宽度
    const containerHeight = 140 // 调整后的容器高度

    const blockWidth = Math.max(100, Math.min(140, (containerWidth - 40) / cols)) // 增大最小宽度
    const blockHeight = Math.max(40, Math.min(60, (containerHeight - 20) / rows)) // 增大最小高度
    const spacingX = 8
    const spacingY = 6

    const x = 15 + col * (blockWidth + spacingX)
    const y = 10 + row * (blockHeight + spacingY)

    console.log(`部门${index}(${total}个): 位置(${x},${y}), 尺寸${blockWidth}x${blockHeight}, 网格${cols}x${rows}`)
    return [[x, y], [x + blockWidth, y], [x + blockWidth, y + blockHeight], [x, y + blockHeight]]
  }
  
  // 根据在用资产数量计算热力强度（normalCount代表在用资产）
  const maxNormalCount = Math.max(...deptStats.map(dept => dept.normalCount || 0))
  console.log('最大在用资产数量:', maxNormalCount)
  
  const result = deptStats.map((dept, index) => ({
    name: dept.departmentName || `部门${dept.departmentId}`,
    coords: generateCoords(index, deptStats.length),
    value: maxNormalCount > 0 ? Math.ceil((dept.normalCount || 0) / maxNormalCount * 100) : 0, // 基于在用资产数量的热力强度
    assetCount: dept.assetCount || 0,
    normalCount: dept.normalCount || 0,
    onlineRate: Number((dept.normalRate || 0).toFixed(1)),
    utilizationRate: Math.max(0, Number((dept.normalRate || 0).toFixed(1)) - Math.random() * 10), // 利用率随机稍低于在线率
    assetTypes: {
      '部门总资产': dept.assetCount || 0,
      '在用设备': dept.normalCount || 0,
      '故障设备': dept.faultCount || 0,
      '维护设备': dept.maintenanceCount || 0
    },
    valueDistribution: [
      { range: '0-10万', count: Math.ceil((dept.assetCount || 0) * 0.2) },
      { range: '10-50万', count: Math.ceil((dept.assetCount || 0) * 0.5) },
      { range: '50-100万', count: Math.ceil((dept.assetCount || 0) * 0.2) },
      { range: '100万以上', count: Math.ceil((dept.assetCount || 0) * 0.1) }
    ],
    maintenanceStatus: {
      '在用': dept.normalCount || 0,
      '故障': dept.faultCount || 0,
      '维护中': dept.maintenanceCount || 0
    }
  }))
  
  console.log('生成的热力图数据:', result)
  return result
}

// 创建默认区域数据（当没有部门数据时使用）
const createDefaultAreaData = () => {
  return [
    {
      name: '暂无数据',
      coords: [[50, 50], [200, 50], [200, 150], [50, 150]],
      value: 0,
      assetCount: 0,
      normalCount: 0,
      onlineRate: 0,
      utilizationRate: 0,
      assetTypes: {
        '部门总资产': 0,
        '在用设备': 0,
        '故障设备': 0,
        '维护设备': 0
      },
      valueDistribution: [],
      maintenanceStatus: {
        '在用': 0,
        '故障': 0,
        '维护中': 0
      }
    }
  ]
}

// 默认热力图颜色函数
const getDefaultHeatColor = (value) => {
  // 将0-100的值映射到颜色
  const intensity = Math.max(0, Math.min(100, value || 0)) / 100

  // 从蓝色到红色的渐变
  if (intensity === 0) return 'rgba(200, 200, 200, 0.3)' // 灰色表示无数据
  if (intensity < 0.2) return `rgba(59, 130, 246, ${0.3 + intensity * 0.4})` // 蓝色
  if (intensity < 0.4) return `rgba(34, 197, 94, ${0.4 + intensity * 0.3})` // 绿色
  if (intensity < 0.6) return `rgba(251, 191, 36, ${0.5 + intensity * 0.3})` // 黄色
  if (intensity < 0.8) return `rgba(249, 115, 22, ${0.6 + intensity * 0.3})` // 橙色
  return `rgba(239, 68, 68, ${0.7 + intensity * 0.3})` // 红色
}

// 计算总资产数量（全部部门或选中部门）
const getTotalAssetCount = () => {
  if (selectedArea.value === 'all') {
    // 显示全部部门时，计算所有部门的总和
    return workbenchData.value?.departmentStatistics?.reduce((total, dept) => total + (dept.assetCount || 0), 0) || 0
  } else {
    // 显示特定部门时，显示该部门的数量
    return selectedAreaInfo.value?.assetCount || 0
  }
}

// 计算在用资产数量
const getTotalNormalCount = () => {
  if (selectedArea.value === 'all') {
    return workbenchData.value?.departmentStatistics?.reduce((total, dept) => total + (dept.normalCount || 0), 0) || 0
  } else {
    return selectedAreaInfo.value?.normalCount || 0
  }
}

// 计算在用率
const getTotalOnlineRate = () => {
  const totalAssets = getTotalAssetCount()
  const normalAssets = getTotalNormalCount()
  if (totalAssets === 0) return 0
  return ((normalAssets / totalAssets) * 100).toFixed(1)
}

// 计算故障设备数量
const getTotalFaultCount = () => {
  if (selectedArea.value === 'all') {
    return workbenchData.value?.departmentStatistics?.reduce((total, dept) => total + (dept.faultCount || 0), 0) || 0
  } else {
    return selectedAreaInfo.value?.assetTypes?.['故障设备'] || 0
  }
}

// 默认KPI指标（数据加载失败时使用）
const getDefaultKpiMetrics = () => [
  {
    label: '资产总数',
    value: '0',
    valueClass: 'kpi-value-default'
  },
  {
    label: '资产总值',
    value: '0.00',
    unit: '万',
    valueClass: 'kpi-value-default'
  },
  {
    label: '在用率',
    value: '0.0%',
    valueClass: 'kpi-value-success'
  },
  {
    label: '维修中',
    value: '0',
    valueClass: 'kpi-value-warning'
  }
]

// 获取可用维度和指标的函数
const fetchDimensions = async () => {
  try {
    const response = await statisticsApi.getDimensions()
    if (response.data && response.data.success) {
      availableDimensions.value = response.data.data || []
    }
  } catch (error) {
    console.error('获取维度失败:', error)
    // 设置默认维度
    availableDimensions.value = [
      { key: 'department', name: '部门' },
      { key: 'location', name: '位置' },
      { key: 'type', name: '资产类型' },
      { key: 'status', name: '状态' }
    ]
  }
}

const fetchMetrics = async () => {
  try {
    const response = await statisticsApi.getMetrics()
    if (response.data && response.data.success) {
      availableMetrics.value = response.data.data || []
    }
  } catch (error) {
    console.error('获取指标失败:', error)
    // 设置默认指标
    availableMetrics.value = [
      { key: 'count', name: '数量' },
      { key: 'totalValue', name: '总价值' },
      { key: 'averageValue', name: '平均价值' }
    ]
  }
}

// 生命周期钩子
onMounted(async () => {
  // 获取可用维度和指标
  await fetchDimensions()
  await fetchMetrics()

  // 等待DOM渲染完成
  await nextTick()

  // 初始化主图表
  initChart()

  // 初始化其他图表
  initFactoryMap()
  initValueDistributionChart()
  initMatrixChart()
  initStatusDistributionChart()
  initAssetTypePieCharts()
  initKpiMiniCharts()

  // 首次加载：获取工作台完整数据（不做筛选）
  await loadWorkbenchData()

  // 确保热力图在数据加载后立即显示所有部门
  console.log('组件挂载完成，确保热力图显示所有部门数据')
  nextTick(() => {
    if (workbenchData.value?.departmentStatistics) {
      console.log('重新初始化热力图以确保显示所有部门:', workbenchData.value.departmentStatistics.length, '个部门')
      initFactoryMap()
    }
  })

  // 设置主题
  document.body.classList.toggle('dark-theme', isDarkTheme.value)
  document.body.classList.toggle('light-theme', !isDarkTheme.value)

  // 监听窗口大小变化，重新调整图表大小
  window.addEventListener('resize', handleResize, { passive: true })
})

// 监听查询参数变化
watch(
  () => [queryParams.dimension, queryParams.metric, selectedStatuses.value],
  () => {
    executeQuery()
  },
  { deep: true }
)

// 方法定义
const initializeData = async () => {
  try {
    // 获取可用维度和指标
    const [dimensionsRes, metricsRes] = await Promise.all([
      statisticsApi.getDimensions(),
      statisticsApi.getMetrics()
    ])
    
    availableDimensions.value = dimensionsRes.data.data || []
    availableMetrics.value = metricsRes.data.data || []
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败')
  }
}

// 加载工作台完整数据（首次加载用）
const loadWorkbenchData = async () => {
  try {
    chartLoading.value = true
    tableLoading.value = true
    console.log('开始加载工作台完整数据')

    const result = await assetAnalyticsApi.getAnalyticsWorkbench()
    console.log('工作台数据响应:', result)

    if (result.success && result.data) {
      const data = result.data
      
      // 存储完整的工作台数据
      workbenchData.value = data
      console.log('存储的工作台数据:', workbenchData.value)

      // 更新KPI数据
      updateKpiData(data.kpiData)

      // 更新筛选选项
      updateFilterOptions(data.filterOptions)

      // 更新各种图表数据
      updateAllCharts(data)

      // 更新表格数据（使用部门统计作为默认显示）
      updateTableDataFromWorkbench(data.departmentStatistics)

      // 刷新厂区地图以显示真实数据（确保第一屏显示所有部门热力图）
      console.log('工作台数据加载完成，立即刷新热力图显示所有部门数据')
      initFactoryMap()

      // 刷新资产类型饼图以显示真实数据
      nextTick(() => {
        initAssetTypePieCharts()
      })

      console.log('工作台数据加载成功，KPI数据:', data.kpiData)
      ElMessage.success('数据加载完成')
    } else {
      console.error('工作台API返回错误:', result.message)
      ElMessage.error(result.message || '数据加载失败')
    }
  } catch (error) {
    console.error('工作台数据加载失败:', error)
    ElMessage.error('数据加载失败: ' + error.message)
  } finally {
    chartLoading.value = false
    tableLoading.value = false
  }
}

const executeQuery = async () => {
  try {
    chartLoading.value = true
    tableLoading.value = true

    // 构建查询请求
    const request = {
      dimension: queryParams.dimension,
      metric: queryParams.metric,
      filters: {
        ...queryParams.filters,
        statuses: selectedStatuses.value
      },
      dateRange: dateRange.value.length === 2 ? {
        startDate: dateRange.value[0],
        endDate: dateRange.value[1],
        field: 'CreatedAt'
      } : null,
      pagination: {
        page: pagination.currentPage,
        size: pagination.pageSize
      }
    }

    console.log('执行查询，请求参数:', request)

    // 使用统计API调用
    const response = await statisticsApi.executeQuery(request)
    console.log('原始API响应:', response)

    if (response && response.success) {
      statisticsResult.value = response.data
      console.log('查询成功，结果数据:', statisticsResult.value)

      // 调试表格数据
      if (statisticsResult.value?.detailedData) {
        console.log('详细数据示例:', statisticsResult.value.detailedData[0])
      }

      // 更新表格数据
      updateTableData()

      // 更新分页信息
      if (statisticsResult.value?.pagination) {
        pagination.total = statisticsResult.value.pagination.totalCount
      }

      // 更新图表
      updateChart()
    } else {
      console.error('API响应不符合预期:', response)
      ElMessage.error('查询结果格式不正确')
    }
  } catch (error) {
    console.error('查询执行失败:', error)
    ElMessage.error('查询执行失败')
  } finally {
    chartLoading.value = false
    tableLoading.value = false
  }
}

const initChart = () => {
  try {
    if (chartInstance.value) {
      chartInstance.value.dispose()
      chartInstance.value = null
    }

    if (!mainChart.value) {
      console.warn('主图表容器未找到')
      return
    }

    // 使用优化的 ECharts 初始化选项
    chartInstance.value = echarts.init(mainChart.value, null, {
      renderer: 'canvas',
      useDirtyRect: true, // 启用脏矩形优化
      useCoarsePointer: true, // 优化触摸设备性能
      pointerSize: 20 // 触摸点大小
    })

    // 图表点击事件
    chartInstance.value.on('click', (params) => {
      handleChartClick(params)
    })

    console.log('主图表初始化成功')
  } catch (error) {
    console.error('主图表初始化失败:', error)
    chartInstance.value = null
  }
}

const updateChart = () => {
  try {
    if (!chartInstance.value || chartInstance.value.isDisposed()) {
      console.log('图表实例不可用，重新初始化')
      initChart()
      return
    }

    if (!statisticsResult.value?.aggregatedData) {
      console.log('图表更新条件不满足:', {
        chartInstance: !!chartInstance.value,
        aggregatedData: !!statisticsResult.value?.aggregatedData,
        dataLength: statisticsResult.value?.aggregatedData?.length
      })
      return
    }

    const data = statisticsResult.value.aggregatedData

    // 验证数据完整性
    if (!Array.isArray(data) || data.length === 0) {
      console.warn('图表数据为空或格式不正确')
      return
    }

    const labels = data.map(item => item.value || '未知')
    const values = data.map(item => item.metricValue || 0)

    console.log('图表数据:', { labels, values, chartType: chartType.value })

    let option = {}

    switch (chartType.value) {
      case 'bar':
        option = createBarChartOption(labels, values)
        break
      case 'pie':
        option = createPieChartOption(data)
        break
      case 'line':
        option = createLineChartOption(labels, values)
        break
      default:
        option = createBarChartOption(labels, values)
    }

    // 验证配置对象
    if (!option || typeof option !== 'object') {
      console.error('图表配置无效')
      return
    }

    console.log('图表配置:', option)
    chartInstance.value.setOption(option, true)
    console.log('图表更新完成')
  } catch (error) {
    console.error('图表更新失败:', error)
    // 尝试重新初始化图表
    setTimeout(() => {
      initChart()
      if (statisticsResult.value?.aggregatedData) {
        updateChart()
      }
    }, 100)
  }
}

const createBarChartOption = (labels, values) => {
  try {
    // 验证输入参数
    if (!Array.isArray(labels) || !Array.isArray(values)) {
      console.error('图表数据格式错误: labels 和 values 必须是数组')
      return null
    }

    if (labels.length === 0 || values.length === 0) {
      console.warn('图表数据为空')
      return {
        title: {
          text: '暂无数据',
          left: 'center',
          top: 'center',
          textStyle: { color: '#999' }
        }
      }
    }

    return {
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#fff',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        textStyle: { color: '#1f2937' },
        formatter: (params) => {
          if (!params || !params[0]) return ''
          const data = params[0]
          return `${data.name}<br/>${data.seriesName || '数值'}: ${data.value}<br/><span style="color: #6b7280; font-size: 12px;">点击可下钻分析</span>`
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: labels,
        axisLine: { lineStyle: { color: '#e5e7eb' } },
        axisTick: { lineStyle: { color: '#e5e7eb' } },
        axisLabel: { color: '#6b7280' }
      },
      yAxis: {
        type: 'value',
        axisLine: { lineStyle: { color: '#e5e7eb' } },
        axisTick: { lineStyle: { color: '#e5e7eb' } },
        axisLabel: { color: '#6b7280' },
        splitLine: { lineStyle: { color: '#f3f4f6' } }
      },
      series: [{
        name: '数量',
        type: 'bar',
        data: values,
        itemStyle: {
          color: '#3b82f6',
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            color: '#2563eb',
            shadowBlur: 10,
            shadowColor: 'rgba(59, 130, 246, 0.3)'
          }
        },
        cursor: 'pointer'
      }]
    }
  } catch (error) {
    console.error('创建柱状图配置失败:', error)
    return null
  }
}

const createPieChartOption = (data) => {
  try {
    // 验证输入参数
    if (!Array.isArray(data)) {
      console.error('饼图数据格式错误: data 必须是数组')
      return null
    }

    if (data.length === 0) {
      console.warn('饼图数据为空')
      return {
        title: {
          text: '暂无数据',
          left: 'center',
          top: 'center',
          textStyle: { color: '#999' }
        }
      }
    }

    // 过滤和验证数据
    const validData = data.filter(item =>
      item &&
      typeof item === 'object' &&
      (item.metricValue !== undefined && item.metricValue !== null) &&
      (item.value !== undefined && item.value !== null)
    )

    if (validData.length === 0) {
      console.warn('饼图没有有效数据')
      return {
        title: {
          text: '暂无有效数据',
          left: 'center',
          top: 'center',
          textStyle: { color: '#999' }
        }
      }
    }

    return {
      tooltip: {
        trigger: 'item',
        formatter: (params) => {
          if (!params) return ''
          return `${params.name}<br/>${params.seriesName || '数值'}: ${params.value} (${params.percent}%)<br/><span style="color: #6b7280; font-size: 12px;">点击可下钻分析</span>`
        },
        backgroundColor: '#fff',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        textStyle: { color: '#1f2937' }
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        textStyle: { color: '#6b7280' }
      },
      series: [{
        name: chartTitle.value || '统计',
        type: 'pie',
        radius: '50%',
        center: ['60%', '50%'],
        data: validData.map(item => ({
          value: Number(item.metricValue) || 0,
          name: String(item.value) || '未知'
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        cursor: 'pointer',
        itemStyle: {
          borderWidth: 2,
          borderColor: '#fff'
        }
      }]
    }
  } catch (error) {
    console.error('创建饼图配置失败:', error)
    return null
  }
}

const createLineChartOption = (labels, values) => {
  return {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: labels
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      type: 'line',
      data: values,
      smooth: true,
      lineStyle: { color: '#3b82f6' },
      itemStyle: { color: '#3b82f6' }
    }]
  }
}

const updateTableData = () => {
  console.log('更新表格数据:', statisticsResult.value?.detailedData)
  if (!statisticsResult.value?.detailedData) {
    tableData.value = []
    return
  }
  
  // 直接赋值详细数据到表格数据源
  tableData.value = statisticsResult.value.detailedData
  console.log('表格数据已更新，共', tableData.value.length, '条记录')
  
  // 更新分页信息
  if (statisticsResult.value?.pagination) {
    pagination.total = statisticsResult.value.pagination.totalCount || tableData.value.length
    console.log('分页信息已更新，总记录数:', pagination.total)
  }
}

const getColumnLabel = (key) => {
  const labelMap = {
    id: 'ID',
    assetCode: '资产编号',
    assetName: '资产名称',
    assetType: '资产类型',
    department: '部门',
    location: '位置',
    status: '状态',
    price: '价值(万元)',
    createdAt: '创建时间'
  }
  return labelMap[key] || key
}

const getColumnWidth = (key) => {
  const widthMap = {
    id: 80,
    assetCode: 120,
    assetName: 150,
    status: 100,
    price: 120
  }
  return widthMap[key] || undefined
}

const getColumnFormatter = (key) => {
  if (key === 'price') {
    return (row) => row[key] ? `¥${Number(row[key]).toFixed(2)}万` : '-'
  }
  if (key === 'createdAt') {
    return (row) => row[key] ? new Date(row[key]).toLocaleDateString() : '-'
  }
  return undefined
}

// 事件处理方法
const handleDimensionChange = () => {
  executeQuery()
}

const handleMetricChange = () => {
  executeQuery()
}

const handleStatusChange = () => {
  executeQuery()
}

const handleFilterChange = () => {
  executeQuery()
}

const handleDateRangeChange = () => {
  queryParams.dateRange = dateRange.value
  executeQuery()
}

const handleChartClick = (params) => {
  // 实现图表点击下钻功能
  const clickedValue = params.name
  const dimension = queryParams.dimension
  
  // 记录下钻历史
  drillDownHistory.value.push({
    dimension: dimension,
    value: clickedValue,
    filters: { ...queryParams.filters }
  })
  
  // 设置筛选条件
  queryParams.filters[dimension] = clickedValue
  
  // 自动切换到合适的下级维度
  switchToDetailDimension(dimension)
  
  executeQuery()
  
  ElMessage.success(`已下钻至 ${getDimensionName(dimension)}: ${clickedValue}`)
}

const switchToDetailDimension = (currentDimension) => {
  // 定义维度下钻路径
  const drillPath = {
    'department': 'assetType',
    'assetType': 'location',
    'location': 'status',
    'status': 'department'
  }

  const nextDimension = drillPath[currentDimension]
  if (nextDimension) {
    queryParams.dimension = nextDimension
  }
}

const getDimensionName = (dimension) => {
  const dimensionMap = {
    'department': '部门',
    'assetType': '资产类型',
    'location': '区域',
    'status': '状态'
  }
  return dimensionMap[dimension] || dimension
}

// 返回上一级功能
const goBackToPreviousLevel = () => {
  if (drillDownHistory.value.length === 0) return
  
  const previousLevel = drillDownHistory.value.pop()
  queryParams.dimension = previousLevel.dimension
  queryParams.filters = { ...previousLevel.filters }
  
  executeQuery()
  
  ElMessage.info('已返回上一级')
}

// 重置所有下钻
const resetDrillDown = () => {
  drillDownHistory.value = []
  queryParams.filters = {}
  queryParams.dimension = 'department' // 重置到默认维度
  executeQuery()
}

// 回到指定层级
const backToLevel = (levelIndex) => {
  // 截取历史到指定层级
  const targetLevel = drillDownHistory.value[levelIndex]
  drillDownHistory.value = drillDownHistory.value.slice(0, levelIndex)
  
  // 恢复该层级的状态
  queryParams.dimension = targetLevel.dimension
  queryParams.filters = { ...targetLevel.filters }
  
  executeQuery()
  
  ElMessage.info(`已返回到 ${getDimensionName(targetLevel.dimension)} 层级`)
}

const handleRowClick = (row) => {
  // 实现行点击事件
  console.log('点击行:', row)
}

const changeChartType = (type) => {
  chartType.value = type
  updateChart()
}

const handleSizeChange = (val) => {
  pagination.pageSize = val
  queryParams.pagination.size = val
  executeQuery()
}

const handleCurrentChange = (val) => {
  pagination.currentPage = val
  queryParams.pagination.page = val
  executeQuery()
}

const resetFilters = () => {
  queryParams.dimension = 'department'
  queryParams.metric = 'count'
  queryParams.filters = {}
  selectedStatuses.value = ['在用', '闲置', '维修中', '报废']
  dateRange.value = []
  pagination.currentPage = 1
  pagination.pageSize = 20
  
  executeQuery()
  
  ElMessage.success('筛选条件已重置')
}

const exportData = () => {
  ElMessageBox.confirm('确定要导出当前数据吗？', '确认导出', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    // 实现数据导出逻辑
    ElMessage.success('数据导出功能开发中...')
  }).catch(() => {
    // 用户取消
  })
}

// 主题切换
const toggleTheme = () => {
  isDarkTheme.value = !isDarkTheme.value
  document.body.classList.toggle('dark-theme', isDarkTheme.value)
  document.body.classList.toggle('light-theme', !isDarkTheme.value)
  
  // 重新渲染所有图表
  nextTick(() => {
    if (factoryMapInstance.value) {
      factoryMapInstance.value.dispose()
      initFactoryMap()
    }
    
    
    if (chartInstance.value) {
      chartInstance.value.dispose()
      updateChart()
    }
    
    if (valueDistributionInstance.value) {
      valueDistributionInstance.value.dispose()
      initValueDistributionChart()
    }
    
    if (matrixInstance.value) {
      matrixInstance.value.dispose()
      initMatrixChart()
    }
    
    if (statusDistributionInstance.value) {
      statusDistributionInstance.value.dispose()
      initStatusDistributionChart()
    }
  })
}

// 区域切换
const handleAreaChange = () => {
  selectedAreaInfo.value = null
  initFactoryMap()
  initValueDistributionChart()
  initStatusDistributionChart()
}

// 初始化厂区地图
const initFactoryMap = () => {
  if (!factoryMap.value) return

  factoryMapInstance.value = echarts.init(factoryMap.value, isDarkTheme.value ? 'dark' : null, {
    renderer: 'canvas',
    useDirtyRect: true,
    useCoarsePointer: true,
    pointerSize: 20
  })

  // 使用后端真实数据，如果没有数据则创建默认显示
  const currentAreaData = getRealAreaData() || createDefaultAreaData()

  console.log('热力图初始化：当前区域数据', currentAreaData.length, '个部门')
  console.log('热力图数据详情:', currentAreaData.map(d => ({ name: d.name, value: d.value, assetCount: d.assetCount })))
  
  // 构建地图数据
  const mapData = currentAreaData.map(area => ({
    name: area.name,
    value: area.value,
    coords: area.coords,
    assetCount: area.assetCount,
    normalCount: area.normalCount,
    onlineRate: area.onlineRate,
    utilizationRate: area.utilizationRate,
    assetTypes: area.assetTypes,
    valueDistribution: area.valueDistribution,
    maintenanceStatus: area.maintenanceStatus
  }))
  
  console.log('热力图构建的地图数据:', mapData)
  
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: params => {
        const data = params.data
        return `
          <div class="tooltip-content">
            <div class="tooltip-title">${params.name}</div>
            <div>总资产数: ${data.assetCount}台</div>
            <div>在用资产: ${data.normalCount}台</div>
            <div>故障设备: ${data.assetTypes?.['故障设备'] || 0}台</div>
            <div>维护设备: ${data.assetTypes?.['维护设备'] || 0}台</div>
            <div>在用率: ${data.onlineRate}%</div>
          </div>
        `
      }
    },
    series: [
      {
        type: 'custom',
        coordinateSystem: 'none',
        renderItem: (params, api) => {
          const dataIndex = params.dataIndex
          const areaData = mapData[dataIndex] // 直接从mapData获取数据

          if (!areaData) {
            console.log('renderItem: 没有找到区域数据，dataIndex:', dataIndex)
            return null
          }

          console.log('renderItem: 渲染区域数据:', areaData.name, '热力值:', areaData.value)

          const coords = areaData.coords
          if (!coords || !coords.length) {
            console.log('renderItem: 坐标数据无效:', coords)
            return null
          }

          // 构建多边形路径
          const points = coords.map(coord => [coord[0], coord[1]])

          // 获取热力图颜色
          const color = getHeatColor ? getHeatColor(areaData.value, isDarkTheme.value) : getDefaultHeatColor(areaData.value)
          console.log('renderItem: 区域', areaData.name, '颜色:', color)

          // 构建多边形形状
          return {
            type: 'polygon',
            shape: {
              points: points
            },
            style: {
              fill: color,
              stroke: isDarkTheme.value ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.5)',
              lineWidth: 1
            },
            // 添加点击交互
            emphasis: {
              style: {
                fill: isDarkTheme.value ? 'rgba(0, 255, 255, 0.8)' : 'rgba(24, 144, 255, 0.8)',
                stroke: isDarkTheme.value ? '#fff' : '#000',
                lineWidth: 2
              }
            },
            // 添加标签
            textContent: {
              style: {
                text: areaData.name,
                fill: isDarkTheme.value ? '#fff' : '#000',
                fontSize: 11, // 增大字体以适应更大的图块
                fontWeight: 'bold'
              }
            },
            textConfig: {
              position: 'inside'
            }
          }
        },
        data: mapData.map((item, index) => ({
          name: item.name,
          value: item.value, // 简化数据结构，直接使用热力值
          itemData: item // 将完整数据存储在itemData中
        })),
        // 添加点击事件
        emphasis: {
          focus: 'self'
        },
        // 添加选中状态
        select: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: isDarkTheme.value ? 'rgba(0, 255, 255, 0.5)' : 'rgba(24, 144, 255, 0.5)'
          }
        }
      }
    ]
  }
  
  factoryMapInstance.value.setOption(option)
  
  // 添加点击事件
  factoryMapInstance.value.on('click', params => {
    console.log('点击事件参数:', params)
    const areaData = params.data?.itemData || currentAreaData.find(area => area.name === params.name)
    if (areaData) {
      console.log('选中区域数据:', areaData)
      selectedAreaInfo.value = areaData
      // 更新资产价值分布图
      updateValueDistributionChart(areaData.valueDistribution)
      // 更新资产状态分布图
      updateStatusDistributionChart(areaData.maintenanceStatus)
    }
  })
  
  // 默认显示全部部门统计（不选中特定区域）
  if (selectedArea.value === 'all') {
    selectedAreaInfo.value = null // 清空选中区域，显示总统计
    console.log('热力图初始化：显示全部部门统计')
  } else if (currentAreaData.length > 0 && !selectedAreaInfo.value) {
    selectedAreaInfo.value = currentAreaData[0]
  }
}


// 初始化资产价值分布图表
const initValueDistributionChart = () => {
  if (!valueDistributionChart.value) return
  
  valueDistributionInstance.value = echarts.init(valueDistributionChart.value, isDarkTheme.value ? 'dark' : null, {
    renderer: 'canvas',
    useDirtyRect: true,
    useCoarsePointer: true,
    pointerSize: 20
  })
  
  // 使用默认的空数据初始化，真实数据将通过updateValueDistributionChartData更新
  const option = {
    backgroundColor: 'transparent',
    title: {
      text: '资产价值区间分布',
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: isDarkTheme.value ? '#eee' : '#333'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        if (params && params.length > 0) {
          const data = params[0]
          return `${data.name}<br/>资产数量: ${data.value}<br/>价值: ${data.data?.totalValue || '未知'}万`
        }
        return ''
      }
    },
    legend: {
      data: ['资产数量'],
      bottom: '0%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        interval: 0,
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      name: '资产数量',
      nameTextStyle: {
        padding: [0, 0, 0, 30]
      }
    },
    series: [{
      name: '资产数量',
      type: 'bar',
      emphasis: {
        focus: 'series'
      },
      data: []
    }]
  }
  
  valueDistributionInstance.value.setOption(option)
}

// 更新资产价值分布图表
const updateValueDistributionChart = (valueData) => {
  if (!valueDistributionInstance.value || !valueData) return
  
  const data = valueData.map(item => item.count)
  
  valueDistributionInstance.value.setOption({
    series: [
      {
        data: data,
        type: 'bar',
        name: '资产数量'
      }
    ],
    legend: {
      data: ['资产数量']
    }
  })
}

// 初始化部门资产矩阵图
const initMatrixChart = () => {
  if (!matrixChart.value) return
  
  matrixInstance.value = echarts.init(matrixChart.value, isDarkTheme.value ? 'dark' : null, {
    renderer: 'canvas',
    useDirtyRect: true,
    useCoarsePointer: true,
    pointerSize: 20
  })
  
  // 使用默认的空数据初始化，真实数据将通过updateMatrixChartData更新
  const option = {
    backgroundColor: 'transparent',
    title: {
      text: '部门资产矩阵',
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: isDarkTheme.value ? '#eee' : '#333'
      }
    },
    tooltip: {
      position: 'top',
      formatter: function(params) {
        if (params.value && params.value.length >= 3) {
          const [x, y, count] = params.value
          const xLabel = params.data?.xLabel || x
          const yLabel = params.data?.yLabel || y
          return `${yLabel} - ${xLabel}<br/>资产数量: ${count}`
        }
        return ''
      }
    },
    grid: {
      left: '3%',
      right: '7%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: [],
      splitArea: {
        show: true
      },
      axisLabel: {
        interval: 0,
        fontSize: 10,
        rotate: 30
      }
    },
    yAxis: {
      type: 'category',
      data: [],
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: 0,
      max: 10,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '0%',
      inRange: {
        color: isDarkTheme.value 
          ? ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffbf', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
          : ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffbf', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026'].reverse()
      }
    },
    series: [{
      name: '资产数量',
      type: 'heatmap',
      data: [],
      label: {
        show: true
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  
  matrixInstance.value.setOption(option)
}

// 初始化资产状态分布图
const initStatusDistributionChart = () => {
  if (!statusDistributionChart.value) return
  
  statusDistributionInstance.value = echarts.init(statusDistributionChart.value, isDarkTheme.value ? 'dark' : null, {
    renderer: 'canvas',
    useDirtyRect: true,
    useCoarsePointer: true,
    pointerSize: 20
  })
  
  const option = {
    backgroundColor: 'transparent',
    title: {
      text: '资产状态分布',
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: isDarkTheme.value ? '#eee' : '#333'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: '0%',
      data: assetStatusDistribution.statuses
    },
    series: [
      {
        name: '资产状态',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: isDarkTheme.value ? '#0f172a' : '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: assetStatusDistribution.statuses.map((status, index) => ({
          value: assetStatusDistribution.data[0][index],
          name: status
        }))
      }
    ]
  }
  
  statusDistributionInstance.value.setOption(option)
}

// 更新资产状态分布图
const updateStatusDistributionChart = (statusData) => {
  if (!statusDistributionInstance.value || !statusData) return
  
  const data = Object.entries(statusData).map(([name, value]) => ({
    name,
    value
  }))
  
  statusDistributionInstance.value.setOption({
    series: [
      {
        data: data
      }
    ],
    legend: {
      data: Object.keys(statusData)
    }
  })
}

// 更新KPI数据
const updateKpiData = (kpiData) => {
  if (!kpiData) return

  // 更新KPI指标显示
  // 这里可以根据实际的KPI显示组件来更新数据
  console.log('更新KPI数据:', kpiData)
}

// 更新筛选选项
const updateFilterOptions = (filterOptions) => {
  if (!filterOptions) return

  // 更新可用维度和指标
  if (filterOptions.assetTypes) {
    availableDimensions.value = [
      { key: 'assetType', name: '资产类型', options: filterOptions.assetTypes },
      { key: 'department', name: '部门', options: filterOptions.departments },
      { key: 'region', name: '区域', options: filterOptions.regions },
      { key: 'status', name: '状态', options: filterOptions.statuses }
    ]
  }

  console.log('更新筛选选项:', filterOptions)
}

// 更新所有图表数据
const updateAllCharts = (data) => {
  if (!data) return


  // 更新价值分布图表
  if (data.valueDistribution && valueDistributionInstance.value) {
    updateValueDistributionChartData(data.valueDistribution)
  }

  // 更新矩阵图表
  if (data.matrixData && matrixInstance.value) {
    updateMatrixChartData(data.matrixData)
  }

  // 更新状态分布图表
  if (data.statusDistribution && statusDistributionInstance.value) {
    updateStatusDistributionChartData(data.statusDistribution)
  }

  // 更新主图表（使用类型统计数据）
  if (data.typeStatistics && data.typeStatistics.length > 0) {
    updateMainChartData(data.typeStatistics)
  }

  console.log('所有图表数据已更新')
}

// 更新主图表
const updateMainChartData = (typeStatistics) => {
  if (!chartInstance.value || !typeStatistics) return

  const labels = typeStatistics.map(item => item.assetTypeName)
  const values = typeStatistics.map(item => item.assetCount)

  const option = createBarChartOption(labels, values)
  if (option) {
    chartInstance.value.setOption(option, true)
  }
}


// 更新价值分布图表
const updateValueDistributionChartData = (valueDistribution) => {
  if (!valueDistributionInstance.value || !valueDistribution) return

  console.log('更新价值分布图表数据:', valueDistribution)

  // 构建图表数据，包含颜色和总价值信息
  const chartData = valueDistribution.assetCounts.map((count, index) => ({
    value: count,
    totalValue: valueDistribution.totalValues ? valueDistribution.totalValues[index] : 0,
    itemStyle: {
      color: valueDistribution.rangeColors && valueDistribution.rangeColors[index] 
        ? valueDistribution.rangeColors[index] 
        : ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'][index % 5]
    }
  }))

  const option = {
    xAxis: {
      data: valueDistribution.valueRanges || []
    },
    series: [{
      name: '资产数量',
      type: 'bar',
      data: chartData
    }],
    tooltip: {
      formatter: function(params) {
        const data = params.data
        return `${params.name}<br/>资产数量: ${data.value}<br/>价值: ${data.totalValue}万`
      }
    }
  }

  valueDistributionInstance.value.setOption(option)
}

// 更新矩阵图表
const updateMatrixChartData = (matrixData) => {
  if (!matrixInstance.value || !matrixData) return

  console.log('更新矩阵图表数据:', matrixData)

  // 计算数据的最大值，用于调整视觉映射范围
  const allValues = matrixData.matrixData.flat()
  const maxValue = Math.max(...allValues, 10) // 至少为10，避免单点数据

  // 转换数据格式，为热力图准备
  const heatmapData = matrixData.matrixData.flatMap((row, i) =>
    row.map((val, j) => ({
      value: [j, i, val],
      xLabel: matrixData.assetTypes[j],
      yLabel: matrixData.departments[i]
    }))
  )

  const option = {
    xAxis: {
      data: matrixData.assetTypes || []
    },
    yAxis: {
      data: matrixData.departments || []
    },
    visualMap: {
      min: 0,
      max: maxValue,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '0%'
    },
    series: [{
      name: '资产数量',
      type: 'heatmap',
      data: heatmapData,
      label: {
        show: true
      }
    }],
    tooltip: {
      formatter: function(params) {
        if (params.data && params.data.value && params.data.value.length >= 3) {
          return `${params.data.yLabel} - ${params.data.xLabel}<br/>资产数量: ${params.data.value[2]}`
        }
        return ''
      }
    }
  }

  matrixInstance.value.setOption(option)
}

// 更新状态分布图表
const updateStatusDistributionChartData = (statusDistribution) => {
  if (!statusDistributionInstance.value || !statusDistribution) return

  const option = {
    series: [{
      data: statusDistribution.statusLabels.map((label, index) => ({
        name: label,
        value: statusDistribution.statusCounts[index]
      }))
    }]
  }

  statusDistributionInstance.value.setOption(option)
}

// 初始化资产类型饼图
const initAssetTypePieCharts = () => {
  // 初始化资产类型饼图
  if (assetTypePieChart1.value) {
    pieChart1Instance.value = echarts.init(assetTypePieChart1.value, isDarkTheme.value ? 'dark' : null)
    
    const option1 = {
      backgroundColor: 'transparent',
      series: [{
        type: 'pie',
        radius: ['50%', '80%'],
        center: ['50%', '50%'],
        data: assetTypeData1.value.map(item => ({
          value: item.percentage,
          name: item.name,
          itemStyle: { color: item.color }
        })),
        label: { show: false },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }
    
    pieChart1Instance.value.setOption(option1)
  }
  
}

// 初始化KPI小图表
const initKpiMiniCharts = () => {
  enhancedKpiMetrics.value.forEach((kpi, index) => {
    if (kpi.chartRef) {
      const chartInstance = echarts.init(kpi.chartRef, isDarkTheme.value ? 'dark' : null)
      
      const option = {
        backgroundColor: 'transparent',
        grid: { top: 0, left: 0, right: 0, bottom: 0 },
        xAxis: { type: 'category', show: false },
        yAxis: { type: 'value', show: false },
        series: [{
          type: 'line',
          data: kpi.chartData,
          smooth: true,
          symbol: 'none',
          lineStyle: { width: 2, color: getKpiChartColor(index) }
        }]
      }
      
      chartInstance.setOption(option)
    }
  })
}

// 获取KPI图表颜色
const getKpiChartColor = (index) => {
  const colors = ['#3b82f6', '#10b981', '#f59e0b', '#8b5cf6']
  return colors[index % colors.length]
}

// 从工作台数据更新表格
const updateTableDataFromWorkbench = (departmentStatistics) => {
  if (!departmentStatistics || !Array.isArray(departmentStatistics)) {
    tableData.value = []
    return
  }

  // 将部门统计数据转换为表格格式
  tableData.value = departmentStatistics.map(dept => ({
    id: dept.departmentId,
    department: dept.departmentName,
    totalAssets: dept.assetCount,
    normalAssets: dept.normalCount,
    faultAssets: dept.faultCount,
    maintenanceAssets: dept.maintenanceCount,
    normalRate: dept.normalRate + '%',
    faultRate: dept.faultRate + '%'
  }))

  console.log('表格数据已从工作台数据更新，共', tableData.value.length, '条记录')
}

// 处理资产类型选择

// 安全销毁图表实例
const safeDisposeChart = (chartInstance, chartName) => {
  try {
    if (chartInstance && typeof chartInstance.dispose === 'function') {
      if (!chartInstance.isDisposed()) {
        chartInstance.dispose()
      }
    }
  } catch (error) {
    console.warn(`销毁图表 ${chartName} 失败:`, error)
  }
}

// 组件卸载前清理
onUnmounted(() => {
  try {
    // 移除事件监听器（确保与添加时的选项一致）
    window.removeEventListener('resize', handleResize, { passive: true })

    // 安全销毁所有图表实例
    safeDisposeChart(chartInstance.value, 'mainChart')
    safeDisposeChart(factoryMapInstance.value, 'factoryMap')
    safeDisposeChart(valueDistributionInstance.value, 'valueDistribution')
    safeDisposeChart(matrixInstance.value, 'matrix')
    safeDisposeChart(statusDistributionInstance.value, 'statusDistribution')
    safeDisposeChart(pieChart1Instance.value, 'pieChart1')

    // 清空引用
    chartInstance.value = null
    factoryMapInstance.value = null
    valueDistributionInstance.value = null
    matrixInstance.value = null
    statusDistributionInstance.value = null
    pieChart1Instance.value = null

    console.log('组件清理完成')
  } catch (error) {
    console.error('组件清理失败:', error)
  }
})

// 防抖函数
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 安全的图表 resize 函数
const safeResizeChart = (chartInstance, chartName) => {
  try {
    if (chartInstance && typeof chartInstance.resize === 'function') {
      // 检查图表是否已经被销毁
      if (!chartInstance.isDisposed()) {
        chartInstance.resize()
      }
    }
  } catch (error) {
    console.warn(`图表 ${chartName} resize 失败:`, error)
  }
}

// 处理窗口大小变化（使用防抖优化性能）
const handleResize = debounce(() => {
  safeResizeChart(chartInstance.value, 'mainChart')
  safeResizeChart(factoryMapInstance.value, 'factoryMap')
  safeResizeChart(valueDistributionInstance.value, 'valueDistribution')
  safeResizeChart(matrixInstance.value, 'matrix')
  safeResizeChart(statusDistributionInstance.value, 'statusDistribution')
  safeResizeChart(pieChart1Instance.value, 'pieChart1')
}, 150) // 150ms防抖延迟
</script>

<style scoped>
/* 性能优化：启用硬件加速和优化渲染 */
.analytics-workbench {
  display: flex;
  width: 100%;
  height: calc(100vh - 64px);
  overflow: hidden;
  background-color: var(--bg-color);
  color: var(--text-color);
  /* 启用硬件加速 */
  transform: translateZ(0);
  will-change: auto;
  /* 优化滚动性能 */
  -webkit-overflow-scrolling: touch;
}

/* 主题变量 */
.dark-theme {
  --bg-color: #0f172a;
  --card-bg: #1e293b;
  --text-color: #e2e8f0;
  --text-primary: #e2e8f0;
  --text-secondary: #94a3b8;
  --border-color: rgba(255, 255, 255, 0.1);
  --hover-color: rgba(255, 255, 255, 0.05);
  --shadow-color: rgba(0, 0, 0, 0.5);
  --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  --header-bg: rgba(30, 41, 59, 0.8);
  --accent-color: #3b82f6;
  --accent-hover: #2563eb;
}

.light-theme {
  --bg-color: #f8fafc;
  --card-bg: #ffffff;
  --text-color: #1e293b;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: rgba(0, 0, 0, 0.1);
  --hover-color: rgba(0, 0, 0, 0.02);
  --shadow-color: rgba(0, 0, 0, 0.1);
  --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --header-bg: rgba(255, 255, 255, 0.9);
  --accent-color: #3b82f6;
  --accent-hover: #2563eb;
}

/* 侧边控制面板 */
.control-panel {
  width: 300px;
  min-width: 300px;
  height: 100%;
  overflow-y: auto;
  background-color: var(--card-bg);
  border-right: 1px solid var(--border-color);
  padding: 20px 0;
  box-shadow: 2px 0 10px var(--shadow-color);
  z-index: 10;
}

/* 面板头部 */
.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px 20px;
  border-bottom: 1px solid var(--border-color);
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo-icon {
  font-size: 24px;
  color: var(--accent-color);
  margin-right: 10px;
}

.panel-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: var(--text-color);
}

.theme-toggle {
  font-size: 20px;
  color: var(--text-color);
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s;
}

.theme-toggle:hover {
  background-color: var(--hover-color);
}

/* 筛选面板 */
.filters-panel {
  padding: 20px;
}

.filter-group {
  margin-bottom: 20px;
}

.filter-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-color);
}

.filter-select {
  width: 100%;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.reset-btn {
  width: 100%;
  margin-top: 10px;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: var(--bg-color);
  /* 性能优化 */
  -webkit-overflow-scrolling: touch;
  contain: layout style paint;
  will-change: scroll-position;
}

/* 页面头部 */
.content-header {
  margin-bottom: 20px;
}

.dashboard-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px;
  color: var(--text-color);
}

.dashboard-subtitle {
  font-size: 14px;
  color: var(--text-color);
  opacity: 0.8;
  margin: 0;
}

/* 增强KPI指标卡片 */
.kpi-section {
  margin-bottom: 25px;
}

.kpi-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.kpi-card.enhanced {
  border-radius: 12px;
  padding: 0;
  box-shadow: 0 4px 12px var(--shadow-color);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.kpi-card.enhanced:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-color);
}

.kpi-content {
  display: flex;
  padding: 20px;
  height: 120px;
}

.kpi-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.kpi-label {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 8px;
  font-weight: 500;
}

.kpi-value-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.kpi-value {
  font-size: 28px;
  font-weight: 700;
  margin: 0;
  line-height: 1;
}

.kpi-unit {
  font-size: 16px;
  opacity: 0.9;
  margin-left: 4px;
}

.kpi-trend {
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend-icon {
  font-size: 14px;
}

.trend-icon.trend-up {
  color: #10b981;
}

.trend-icon.trend-down {
  color: #ef4444;
}

.trend-text {
  font-size: 12px;
  font-weight: 600;
}

.kpi-chart {
  width: 80px;
  height: 80px;
  position: relative;
  align-self: center;
}

.mini-chart {
  width: 100%;
  height: 100%;
}

/* KPI卡片主题颜色 */
.kpi-card-blue {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.kpi-card-green {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.kpi-card-purple {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.kpi-value-blue,
.kpi-value-green,
.kpi-value-purple {
  color: white;
}

.kpi-value-danger {
  color: #ef4444;
}

/* Enhanced KPI card styles */
.kpi-content {
  padding: 20px;
  position: relative;
  z-index: 2;
}

.kpi-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.kpi-label {
  font-size: 14px;
  opacity: 0.9;
  font-weight: 500;
}

.kpi-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  opacity: 0.9;
}

.trend-up { color: #10b981; }
.trend-down { color: #ef4444; }

.kpi-body {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.kpi-value-section {
  flex: 1;
}

.kpi-value {
  font-size: 28px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 4px;
}

.kpi-value-blue { color: white; }
.kpi-value-green { color: white; }
.kpi-value-orange { color: white; }
.kpi-value-purple { color: white; }

.kpi-mini-chart {
  width: 60px;
  height: 40px;
  flex-shrink: 0;
}

/* Factory map layout - wide version */
.factory-map-wide {
  position: relative;
  width: 100%;
  height: 400px;
  contain: layout style paint;
  transform: translateZ(0);
}

.chart-section {
  background: var(--card-bg);
  border-radius: 8px;
  padding: 16px;
  box-shadow: var(--card-shadow);
}

.chart-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: var(--text-primary);
}

.asset-type-pie-chart {
  width: 100%;
  height: 280px;
}

.asset-type-legend {
  margin-top: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 8px;
  flex-shrink: 0;
}

.legend-label {
  flex: 1;
  color: var(--text-secondary);
}

.legend-value {
  font-weight: 600;
  color: var(--text-primary);
}

/* Asset type card styles */
.asset-type-card {
  margin-bottom: 20px;
}

.asset-type-content {
  padding: 0;
}

.asset-type-pie-chart {
  width: 100%;
  height: 280px;
}

.asset-type-legend {
  margin-top: 16px;
}

.asset-type-legend .legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
}

.asset-type-legend .legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 8px;
  flex-shrink: 0;
}

.asset-type-legend .legend-label {
  flex: 1;
  color: var(--text-secondary);
}

.asset-type-legend .legend-value {
  font-weight: 600;
  color: var(--text-primary);
}

/* 调试信息 */
.debug-section {
  margin-bottom: 20px;
}

.debug-card {
  background-color: var(--card-bg);
  border-radius: 8px;
  overflow: hidden;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.debug-title {
  margin: 0;
  font-size: 16px;
}

.debug-content {
  max-height: 400px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 12px;
  white-space: pre-wrap;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

/* 网格布局 */
.grid-layout {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.second-row {
  grid-template-columns: 1fr 1fr;
}

/* 左侧列 */
.left-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 右侧列 */
.right-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 卡片样式 */
:deep(.el-card) {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 4px 6px var(--shadow-color);
  transition: all 0.3s;
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.el-card:hover) {
  box-shadow: 0 8px 15px var(--shadow-color);
}

:deep(.el-card__header) {
  padding: 15px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--header-bg);
}

:deep(.el-card__body) {
  padding: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 卡片头部 */
.card-header, .chart-header, .table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3, .chart-header h3, .table-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
}

.header-actions {
  display: flex;
  gap: 10px;
}

/* 厂区地图 */
.map-card {
  flex: 1;
}

.factory-map-container {
  position: relative;
  width: 100%;
  height: 300px;
  /* 性能优化 */
  contain: layout style paint;
  transform: translateZ(0);
}

.factory-map {
  width: 100%;
  height: 100%;
  /* 性能优化 */
  will-change: transform;
}

.map-legend {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background-color: var(--card-bg);
  padding: 5px 10px;
  border-radius: 4px;
  box-shadow: 0 2px 4px var(--shadow-color);
  z-index: 1;
}

.legend-title {
  font-size: 12px;
  margin-bottom: 5px;
  color: var(--text-color);
}

.legend-gradient {
  height: 10px;
  width: 100px;
  background: linear-gradient(to right, 
    rgba(0, 32, 96, 0.6), 
    rgba(0, 64, 128, 0.7), 
    rgba(0, 128, 192, 0.8), 
    rgba(0, 192, 255, 0.9), 
    rgba(64, 224, 255, 1.0)
  );
  border-radius: 2px;
  margin-bottom: 2px;
}

.dark-theme .legend-gradient {
  background: linear-gradient(to right, 
    rgba(0, 32, 96, 0.6), 
    rgba(0, 64, 128, 0.7), 
    rgba(0, 128, 192, 0.8), 
    rgba(0, 192, 255, 0.9), 
    rgba(64, 224, 255, 1.0)
  );
}

.light-theme .legend-gradient {
  background: linear-gradient(to right, 
    rgba(240, 249, 255, 0.8), 
    rgba(189, 229, 255, 0.8), 
    rgba(107, 174, 214, 0.8), 
    rgba(49, 130, 189, 0.8), 
    rgba(8, 81, 156, 0.8)
  );
}

.legend-labels {
  display: flex;
  justify-content: space-between;
  font-size: 10px;
  color: var(--text-color);
}

.area-info {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: var(--card-bg);
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 4px var(--shadow-color);
  max-width: 200px;
  z-index: 1;
}

.area-info h4 {
  margin: 0 0 10px;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
}

.area-stats {
  font-size: 12px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.stat-label {
  color: var(--text-color);
  opacity: 0.8;
}

.stat-value {
  font-weight: 500;
  color: var(--text-color);
}

.asset-types {
  margin-top: 10px;
  border-top: 1px solid var(--border-color);
  padding-top: 5px;
}

.asset-types-title {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 5px;
  color: var(--text-color);
}

.asset-type-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
  font-size: 11px;
}

.asset-type-label {
  color: var(--text-color);
  opacity: 0.8;
}

.asset-type-value {
  font-weight: 500;
  color: var(--text-color);
}


/* 统计卡片 */
.stats-card {
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.stat-item {
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  transition: all 0.3s;
}

.stat-item:hover {
  transform: translateY(-3px);
}

.stat-item p {
  margin: 0 0 5px;
  font-size: 14px;
  opacity: 0.8;
}

.stat-item h3 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.stat-item.online {
  background-color: rgba(59, 130, 246, 0.2);
}

.stat-item.idle {
  background-color: rgba(16, 185, 129, 0.2);
}

.stat-item.maintenance {
  background-color: rgba(245, 158, 11, 0.2);
}

.stat-item.utilization {
  background-color: rgba(139, 92, 246, 0.2);
}

/* 图表卡片 */
.chart-card {
  margin-bottom: 20px;
  flex: 1;
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 250px;
}

.chart-canvas {
  width: 100%;
  height: 100%;
  min-height: 250px;
}

/* 表格区域 */
.table-section {
  margin-bottom: 20px;
}

.table-card {
  background-color: var(--card-bg);
}

.table-header {
  margin-bottom: 0;
}

.table-summary {
  display: flex;
  align-items: center;
  gap: 10px;
}

.summary-text {
  font-size: 14px;
  color: var(--text-color);
  opacity: 0.8;
}

.data-table {
  width: 100%;
  margin-bottom: 15px;
}

:deep(.el-table) {
  background-color: var(--card-bg);
  color: var(--text-color);
}

:deep(.el-table th) {
  background-color: var(--header-bg);
  color: var(--text-color);
  font-weight: 600;
}

:deep(.el-table td), :deep(.el-table th.is-leaf) {
  border-bottom: 1px solid var(--border-color);
}

:deep(.el-table--border), :deep(.el-table--group) {
  border: 1px solid var(--border-color);
}

:deep(.el-table--border .el-table__cell) {
  border-right: 1px solid var(--border-color);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: var(--hover-color);
}

:deep(.el-table__body tr.hover-row > td) {
  background-color: var(--hover-color);
}

.empty-data {
  padding: 40px 0;
  text-align: center;
  color: var(--text-color);
  opacity: 0.5;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding-top: 15px;
}

:deep(.el-pagination) {
  color: var(--text-color);
}

:deep(.el-pagination .el-pagination__total),
:deep(.el-pagination .el-pagination__jump) {
  color: var(--text-color);
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  background-color: var(--card-bg);
  color: var(--text-color);
}

:deep(.el-pagination .el-pager li) {
  background-color: var(--card-bg);
  color: var(--text-color);
}

:deep(.el-pagination .el-pager li.active) {
  color: var(--accent-color);
  font-weight: bold;
}

:deep(.el-pagination .el-select .el-input) {
  color: var(--text-color);
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .grid-layout {
    grid-template-columns: 1fr;
  }
  
  .second-row {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .analytics-workbench {
    flex-direction: column;
    height: auto;
  }
  
  .control-panel {
    width: 100%;
    height: auto;
    max-height: 300px;
  }
  
  .kpi-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 热力图和资产类型区域高度调整 - 保持一致 */
.factory-map-compact {
  height: 180px !important; /* 热力图高度 */
}

.factory-map-reduced {
  height: 160px !important; /* 内部图表高度 */
}

.asset-type-compact {
  height: 180px !important; /* 与热力图保持一致的高度 */
}

.asset-type-reduced {
  height: 160px !important; /* 内部图表高度与热力图一致 */
}

/* 确保容器高度一致 */
.map-card {
  min-height: 200px !important;
  max-height: 200px !important;
}

.asset-type-card {
  min-height: 200px !important; /* 与热力图卡片高度一致 */
  max-height: 200px !important;
}

/* 调整图例和信息区域 */
.map-legend {
  bottom: 2px !important;
  height: 15px !important;
  font-size: 10px !important;
}

.area-info {
  max-height: 60px !important;
  overflow-y: auto;
  font-size: 11px !important;
}

.asset-type-legend {
  max-height: 50px !important;
  overflow-y: auto;
  font-size: 10px !important;
}

/* 进一步压缩卡片内边距 */
.map-card :deep(.el-card__body) {
  padding: 8px !important;
}

.asset-type-card :deep(.el-card__body) {
  padding: 8px !important;
}

.map-card :deep(.el-card__header) {
  padding: 8px 12px !important;
}

.asset-type-card :deep(.el-card__header) {
  padding: 8px 12px !important;
}
</style>