<!-- File: frontend/src/views/asset/AssetAnalyticsWorkbench.vue -->
<!-- Description: 企业级智能资产分析工作台 - 交互式数据探索平台 -->

<template>
  <div class="analytics-workbench" :class="{ 'dark-theme': isDarkTheme, 'light-theme': !isDarkTheme }">
    <!-- 侧边控制面板 -->
    <aside class="control-panel">
      <div class="panel-header">
        <div class="logo-container">
          <el-icon class="logo-icon"><DataAnalysis /></el-icon>
          <h1 class="panel-title">资产分析工作台</h1>
        </div>
        <!-- 主题切换按钮 -->
        <el-button 
          type="text" 
          class="theme-toggle"
          @click="toggleTheme"
        >
          <el-icon v-if="isDarkTheme"><Sunny /></el-icon>
          <el-icon v-else><Moon /></el-icon>
        </el-button>
      </div>

      <div class="filters-panel">
        <!-- 分析维度选择 -->
        <div class="filter-group">
          <label class="filter-label">分析维度</label>
          <el-select
            v-model="queryParams.dimension"
            placeholder="选择分析维度"
            class="filter-select"
            @change="handleDimensionChange"
          >
            <el-option
              v-for="dim in availableDimensions"
              :key="dim.key"
              :label="dim.name"
              :value="dim.key"
            />
          </el-select>
        </div>

        <!-- 度量指标选择 -->
        <div class="filter-group">
          <label class="filter-label">度量指标</label>
          <el-select
            v-model="queryParams.metric"
            placeholder="选择度量指标"
            class="filter-select"
            @change="handleMetricChange"
          >
            <el-option
              v-for="metric in availableMetrics"
              :key="metric.key"
              :label="metric.name"
              :value="metric.key"
            />
          </el-select>
        </div>

        <!-- 资产状态筛选 -->
        <div class="filter-group">
          <label class="filter-label">资产状态</label>
          <div class="checkbox-group">
            <el-checkbox
              v-for="status in assetStatuses"
              :key="status"
              v-model="selectedStatuses"
              :label="status"
              @change="handleStatusChange"
            >
              {{ status }}
            </el-checkbox>
          </div>
        </div>

        <!-- 部门筛选 -->
        <div class="filter-group">
          <label class="filter-label">部门</label>
          <el-select
            v-model="queryParams.filters.department"
            placeholder="选择部门"
            class="filter-select"
            clearable
            @change="handleFilterChange"
          >
            <el-option label="所有部门" value="" />
            <el-option
              v-for="dept in departments"
              :key="dept"
              :label="dept"
              :value="dept"
            />
          </el-select>
        </div>

        <!-- 时间范围筛选 -->
        <div class="filter-group">
          <label class="filter-label">时间范围</label>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            class="filter-select"
            @change="handleDateRangeChange"
          />
        </div>

        <!-- 重置按钮 -->
        <el-button
          type="info"
          class="reset-btn"
          @click="resetFilters"
        >
          重置筛选
        </el-button>
      </div>
    </aside>

    <!-- 主内容区域 -->
    <main class="main-content">
      <!-- 页面头部 -->
      <header class="content-header">
        <h2 class="dashboard-title">{{ dashboardTitle }}</h2>
        <p class="dashboard-subtitle">{{ dashboardSubtitle }}</p>
      </header>

      <!-- KPI指标卡片 -->
      <section class="kpi-section">
        <div class="kpi-container">
          <div
            v-for="kpi in kpiMetrics"
            :key="kpi.label"
            class="kpi-card"
          >
            <p class="kpi-label">{{ kpi.label }}</p>
            <p class="kpi-value" :class="kpi.valueClass">
              {{ kpi.value }}
              <span v-if="kpi.unit" class="kpi-unit">{{ kpi.unit }}</span>
            </p>
          </div>
        </div>
      </section>

      <!-- 数据调试信息 -->
      <section v-if="showDebugInfo" class="debug-section">
        <el-card class="debug-card">
          <template #header>
            <div class="debug-header">
              <h3 class="debug-title">数据调试信息</h3>
              <el-button type="warning" size="small" @click="showDebugInfo = false">关闭</el-button>
            </div>
          </template>
          <div class="debug-content">
            <h4>统计结果摘要:</h4>
            <pre>{{ JSON.stringify(statisticsResult?.summary || {}, null, 2) }}</pre>
            <h4>聚合数据 (前5项):</h4>
            <pre>{{ JSON.stringify(statisticsResult?.aggregatedData?.slice(0, 5) || [], null, 2) }}</pre>
            <h4>详细数据 (前2项):</h4>
            <pre>{{ JSON.stringify(tableData.slice(0, 2) || [], null, 2) }}</pre>
            <h4>表格列:</h4>
            <pre>{{ JSON.stringify(tableColumns || [], null, 2) }}</pre>
          </div>
        </el-card>
      </section>

      <!-- 主内容区 -->
      <div class="grid-layout">
        <!-- 左侧：资产分布与地图 -->
        <div class="left-column">
          <!-- 厂区地图容器 -->
          <el-card class="map-card">
            <template #header>
              <div class="card-header">
                <h3>厂区资产分布热力图</h3>
                <div class="header-actions">
                  <el-radio-group v-model="selectedArea" size="small" @change="handleAreaChange">
                    <el-radio-button label="生产车间">生产车间</el-radio-button>
                    <el-radio-button label="仓储区域">仓储区域</el-radio-button>
                    <el-radio-button label="办公区域">办公区域</el-radio-button>
                  </el-radio-group>
                </div>
              </div>
            </template>
            <div class="factory-map-container">
              <div ref="factoryMap" class="factory-map"></div>
              <div class="map-legend">
                <div class="legend-title">资产密度</div>
                <div class="legend-gradient"></div>
                <div class="legend-labels">
                  <span>低</span>
                  <span>高</span>
                </div>
              </div>
              <div class="area-info" v-if="selectedAreaInfo">
                <h4>{{ selectedAreaInfo.name }}</h4>
                <div class="area-stats">
                  <div class="stat-row">
                    <span class="stat-label">资产总数:</span>
                    <span class="stat-value">{{ selectedAreaInfo.assetCount }}</span>
                  </div>
                  <div class="stat-row">
                    <span class="stat-label">在线率:</span>
                    <span class="stat-value">{{ selectedAreaInfo.onlineRate }}%</span>
                  </div>
                  <div class="stat-row">
                    <span class="stat-label">利用率:</span>
                    <span class="stat-value">{{ selectedAreaInfo.utilizationRate }}%</span>
                  </div>
                  <div class="asset-types" v-if="selectedAreaInfo.assetTypes">
                    <div class="asset-types-title">资产类型分布:</div>
                    <div class="asset-type-row" v-for="(count, type) in selectedAreaInfo.assetTypes" :key="type">
                      <span class="asset-type-label">{{ type }}:</span>
                      <span class="asset-type-value">{{ count }}台</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 访问时段分析 -->
          <el-card class="time-series-card">
            <template #header>
              <div class="card-header">
                <h3>设备使用时段分析</h3>
                <div class="header-actions">
                  <el-select v-model="selectedAssetType" size="small" @change="handleAssetTypeChange">
                    <el-option label="全部" value="全部" />
                    <el-option label="工控机" value="工控机" />
                    <el-option label="测试设备" value="测试设备" />
                    <el-option label="办公设备" value="办公设备" />
                  </el-select>
                </div>
              </div>
            </template>
            <div ref="timeSeriesChart" class="time-series-chart"></div>
          </el-card>
        </div>

        <!-- 右侧：统计与图表 -->
        <div class="right-column">
          <!-- 统计卡片 -->
          <el-card class="stats-card">
            <div class="stats-grid">
              <div class="stat-item online">
                <p class="stat-label">在线设备</p>
                <h3 class="stat-value">86%</h3>
              </div>
              <div class="stat-item idle">
                <p class="stat-label">空闲率</p>
                <h3 class="stat-value">14%</h3>
              </div>
              <div class="stat-item maintenance">
                <p class="stat-label">维护中</p>
                <h3 class="stat-value">5%</h3>
              </div>
              <div class="stat-item utilization">
                <p class="stat-label">平均利用率</p>
                <h3 class="stat-value">78%</h3>
              </div>
            </div>
          </el-card>

          <!-- 资产状态环形图 -->
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <h3 class="chart-title">{{ chartTitle }}</h3>
                <div class="chart-actions">
                  <!-- 下钻控制按钮 -->
                  <div class="drill-controls" v-if="drillDownHistory.length > 0">
                    <el-button
                      type="info"
                      size="small"
                      @click="goBackToPreviousLevel"
                    >
                      <el-icon><ArrowLeft /></el-icon>
                      返回上级
                    </el-button>
                    <el-button
                      type="warning"
                      size="small"
                      @click="resetDrillDown"
                    >
                      <el-icon><Refresh /></el-icon>
                      重置下钻
                    </el-button>
                  </div>
                  
                  <!-- 图表类型切换 -->
                  <el-button-group>
                    <el-button
                      :type="chartType === 'bar' ? 'primary' : 'default'"
                      size="small"
                      @click="changeChartType('bar')"
                    >
                      柱状图
                    </el-button>
                    <el-button
                      :type="chartType === 'pie' ? 'primary' : 'default'"
                      size="small"
                      @click="changeChartType('pie')"
                    >
                      饼图
                    </el-button>
                    <el-button
                      :type="chartType === 'line' ? 'primary' : 'default'"
                      size="small"
                      @click="changeChartType('line')"
                    >
                      折线图
                    </el-button>
                  </el-button-group>
                </div>
              </div>
            </template>
            
            <div class="chart-container">
              <div
                ref="mainChart"
                class="chart-canvas"
                v-loading="chartLoading"
              ></div>
            </div>
          </el-card>
          
          <!-- 资产状态分布图 -->
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <h3 class="chart-title">资产状态分布</h3>
              </div>
            </template>
            <div class="chart-container">
              <div ref="statusDistributionChart" class="chart-canvas"></div>
            </div>
          </el-card>
        </div>
      </div>
      
      <!-- 第二行图表 -->
      <div class="grid-layout second-row">
        <!-- 资产价值分布图 -->
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <h3 class="chart-title">资产价值区间分布</h3>
            </div>
          </template>
          <div class="chart-container">
            <div ref="valueDistributionChart" class="chart-canvas"></div>
          </div>
        </el-card>
        
        <!-- 部门资产矩阵图 -->
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <h3 class="chart-title">部门资产矩阵</h3>
            </div>
          </template>
          <div class="chart-container">
            <div ref="matrixChart" class="chart-canvas"></div>
          </div>
        </el-card>
      </div>

      <!-- 数据表格区域 -->
      <section class="table-section">
        <el-card class="table-card">
          <template #header>
            <div class="table-header">
              <h3 class="table-title">数据明细</h3>
              <div class="table-summary">
                <span class="summary-text">显示 {{ statisticsResult?.summary?.totalRecords || 0 }} 条记录</span>
                <el-button type="info" size="small" @click="showDebugInfo = !showDebugInfo">
                  {{ showDebugInfo ? '隐藏调试' : '显示调试' }}
                </el-button>
                <el-button
                  type="primary"
                  size="small"
                  @click="exportData"
                >
                  导出数据
                </el-button>
              </div>
            </div>
          </template>

          <div v-if="!tableData.length && !tableLoading" class="empty-data">
            <el-empty description="暂无数据" />
          </div>

          <el-table
            v-else
            :data="tableData"
            v-loading="tableLoading"
            class="data-table"
            stripe
            border
            @row-click="handleRowClick"
          >
            <!-- 静态定义关键列 -->
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="assetCode" label="资产编号" width="120" />
            <el-table-column prop="assetName" label="资产名称" width="150" />
            <el-table-column prop="assetType" label="资产类型" />
            <el-table-column prop="department" label="部门" />
            <el-table-column prop="location" label="位置" />
            <el-table-column prop="status" label="状态" width="100" />
            <el-table-column 
              prop="price" 
              label="价值(万元)" 
              width="120"
              :formatter="(row) => row.price ? `¥${Number(row.price).toFixed(2)}万` : '-'"
            />
            <el-table-column 
              prop="createdAt" 
              label="创建时间"
              :formatter="(row) => row.createdAt ? new Date(row.createdAt).toLocaleDateString() : '-'"
            />
            <el-table-column prop="managerName" label="负责人" width="100" />
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-card>
      </section>
    </main>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { DataAnalysis, ArrowLeft, Refresh, Sunny, Moon } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { statisticsApi } from '@/api/statistics'
import axios from 'axios'
import { 
  factoryAreas, 
  getHeatColor, 
  timeSeriesData, 
  assetValueDistribution, 
  assetStatusDistribution,
  departmentAssetMatrix 
} from '@/assets/factory-layout.js'

// 响应式数据
const mainChart = ref(null)
const factoryMap = ref(null)
const timeSeriesChart = ref(null)
const valueDistributionChart = ref(null)
const matrixChart = ref(null)
const statusDistributionChart = ref(null)
const chartInstance = ref(null)
const factoryMapInstance = ref(null)
const timeSeriesInstance = ref(null)
const valueDistributionInstance = ref(null)
const matrixInstance = ref(null)
const statusDistributionInstance = ref(null)
const chartLoading = ref(false)
const tableLoading = ref(false)
const chartType = ref('bar')
const showDebugInfo = ref(false)
const isDarkTheme = ref(true)
const selectedArea = ref('生产车间')
const selectedAreaInfo = ref(null)
const selectedAssetType = ref('全部')

// 查询参数
const queryParams = reactive({
  dimension: 'department',
  metric: 'count',
  filters: {},
  dateRange: null,
  pagination: {
    page: 1,
    size: 20
  }
})

// 筛选条件
const selectedStatuses = ref(['在用', '闲置', '维修中', '报废'])
const dateRange = ref([])

// 可选项数据
const availableDimensions = ref([
  { key: 'department', name: '部门' },
  { key: 'location', name: '位置' },
  { key: 'type', name: '资产类型' },
  { key: 'status', name: '状态' }
])
const availableMetrics = ref([
  { key: 'count', name: '数量' },
  { key: 'totalValue', name: '总价值' },
  { key: 'averageValue', name: '平均价值' }
])
const assetStatuses = ref(['在用', '闲置', '维修中', '报废'])
const departments = ref(['技术部', '销售部', '市场部', '行政部', '财务部'])

// 统计结果数据
const statisticsResult = ref(null)
const tableData = ref([])
const tableColumns = ref([])

// 下钻历史数据
const drillDownHistory = ref([])

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 计算属性
const dashboardTitle = computed(() => {
  if (queryParams.filters.department) {
    return `${queryParams.filters.department} - 资产洞察`
  }
  return '资产全局洞察'
})

const dashboardSubtitle = computed(() => {
  if (queryParams.filters.department) {
    return `当前已筛选"部门"为"${queryParams.filters.department}"`
  }
  return '当前展示所有资产的概览。您可以在左侧面板进行筛选和维度切换。'
})

const chartTitle = computed(() => {
  const dimensionName = availableDimensions.value.find(d => d.key === queryParams.dimension)?.name || '未知维度'
  const metricName = availableMetrics.value.find(m => m.key === queryParams.metric)?.name || '未知指标'
  return `按${dimensionName}分析 - ${metricName}`
})

const kpiMetrics = computed(() => {
  if (!statisticsResult.value?.summary) return []
  
  const summary = statisticsResult.value.summary
  const totalValue = summary.totalValue || 0
  const totalRecords = summary.totalRecords || 0
  const averageValue = summary.averageValue || 0
  
  // 计算在用率等关键指标
  const detailedData = statisticsResult.value.detailedData || []
  const activeCount = detailedData.filter(item => item.status === '在用').length
  const activeRate = totalRecords > 0 ? ((activeCount / totalRecords) * 100).toFixed(1) : 0
  const maintenanceCount = detailedData.filter(item => item.status === '维修中').length
  
  return [
    {
      label: '资产总数',
      value: totalRecords.toLocaleString(),
      valueClass: 'kpi-value-default'
    },
    {
      label: '资产总值',
      value: totalValue.toFixed(2),
      unit: '万',
      valueClass: 'kpi-value-default'
    },
    {
      label: '在用率',
      value: `${activeRate}%`,
      valueClass: 'kpi-value-success'
    },
    {
      label: '维修中',
      value: maintenanceCount,
      valueClass: 'kpi-value-warning'
    }
  ]
})

// 获取可用维度和指标的函数
const fetchDimensions = async () => {
  try {
    const response = await statisticsApi.getDimensions()
    if (response.data && response.data.success) {
      availableDimensions.value = response.data.data || []
    }
  } catch (error) {
    console.error('获取维度失败:', error)
    // 设置默认维度
    availableDimensions.value = [
      { key: 'department', name: '部门' },
      { key: 'location', name: '位置' },
      { key: 'type', name: '资产类型' },
      { key: 'status', name: '状态' }
    ]
  }
}

const fetchMetrics = async () => {
  try {
    const response = await statisticsApi.getMetrics()
    if (response.data && response.data.success) {
      availableMetrics.value = response.data.data || []
    }
  } catch (error) {
    console.error('获取指标失败:', error)
    // 设置默认指标
    availableMetrics.value = [
      { key: 'count', name: '数量' },
      { key: 'totalValue', name: '总价值' },
      { key: 'averageValue', name: '平均价值' }
    ]
  }
}

// 生命周期钩子
onMounted(async () => {
  // 获取可用维度和指标
  await fetchDimensions()
  await fetchMetrics()

  // 等待DOM渲染完成
  await nextTick()

  // 初始化主图表
  initChart()

  // 初始化其他图表
  initFactoryMap()
  initTimeSeriesChart()
  initValueDistributionChart()
  initMatrixChart()
  initStatusDistributionChart()

  // 执行初始查询
  await executeQuery()

  // 设置主题
  document.body.classList.toggle('dark-theme', isDarkTheme.value)
  document.body.classList.toggle('light-theme', !isDarkTheme.value)

  // 监听窗口大小变化，重新调整图表大小
  window.addEventListener('resize', handleResize)
})

// 监听查询参数变化
watch(
  () => [queryParams.dimension, queryParams.metric, selectedStatuses.value],
  () => {
    executeQuery()
  },
  { deep: true }
)

// 方法定义
const initializeData = async () => {
  try {
    // 获取可用维度和指标
    const [dimensionsRes, metricsRes] = await Promise.all([
      statisticsApi.getDimensions(),
      statisticsApi.getMetrics()
    ])
    
    availableDimensions.value = dimensionsRes.data.data || []
    availableMetrics.value = metricsRes.data.data || []
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败')
  }
}

const executeQuery = async () => {
  try {
    chartLoading.value = true
    tableLoading.value = true
    
    // 构建查询请求
    const request = {
      dimension: queryParams.dimension,
      metric: queryParams.metric,
      filters: {
        ...queryParams.filters,
        statuses: selectedStatuses.value
      },
      dateRange: dateRange.value.length === 2 ? {
        startDate: dateRange.value[0],
        endDate: dateRange.value[1],
        field: 'CreatedAt'
      } : null,
      pagination: {
        page: pagination.currentPage,
        size: pagination.pageSize
      }
    }
    
    console.log('执行查询，请求参数:', request)
    
    // 直接使用axios调用API以方便调试
    const response = await axios.post('/api/v2/statistics/query', request)
    console.log('原始API响应:', response)
    
    if (response.data && response.data.success) {
      statisticsResult.value = response.data.data
      console.log('查询成功，结果数据:', statisticsResult.value)
      
      // 调试表格数据
      if (statisticsResult.value?.detailedData) {
        console.log('详细数据示例:', statisticsResult.value.detailedData[0])
      }
      
      // 更新表格数据
      updateTableData()
      
      // 更新分页信息
      if (statisticsResult.value?.pagination) {
        pagination.total = statisticsResult.value.pagination.totalCount
      }
      
      // 更新图表
      updateChart()
    } else {
      console.error('API响应不符合预期:', response.data)
      ElMessage.error('查询结果格式不正确')
    }
  } catch (error) {
    console.error('查询执行失败:', error)
    ElMessage.error('查询执行失败')
  } finally {
    chartLoading.value = false
    tableLoading.value = false
  }
}

const initChart = () => {
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }
  
  chartInstance.value = echarts.init(mainChart.value)
  
  // 图表点击事件
  chartInstance.value.on('click', (params) => {
    handleChartClick(params)
  })
  
  // 响应式图表
  window.addEventListener('resize', () => {
    chartInstance.value?.resize()
  })
}

const updateChart = () => {
  if (!chartInstance.value || !statisticsResult.value?.aggregatedData) {
    console.log('图表更新条件不满足:', {
      chartInstance: !!chartInstance.value,
      aggregatedData: !!statisticsResult.value?.aggregatedData,
      dataLength: statisticsResult.value?.aggregatedData?.length
    })
    return
  }

  const data = statisticsResult.value.aggregatedData
  const labels = data.map(item => item.value)
  const values = data.map(item => item.metricValue)

  console.log('图表数据:', { labels, values, chartType: chartType.value })

  let option = {}

  switch (chartType.value) {
    case 'bar':
      option = createBarChartOption(labels, values)
      break
    case 'pie':
      option = createPieChartOption(data)
      break
    case 'line':
      option = createLineChartOption(labels, values)
      break
  }

  console.log('图表配置:', option)
  chartInstance.value.setOption(option, true)
  console.log('图表更新完成')
}

const createBarChartOption = (labels, values) => {
  return {
    tooltip: {
      trigger: 'axis',
      backgroundColor: '#fff',
      borderColor: '#e5e7eb',
      borderWidth: 1,
      textStyle: { color: '#1f2937' },
      formatter: (params) => {
        const data = params[0]
        return `${data.name}<br/>${data.seriesName}: ${data.value}<br/><span style="color: #6b7280; font-size: 12px;">点击可下钻分析</span>`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: labels,
      axisLine: { lineStyle: { color: '#e5e7eb' } },
      axisTick: { lineStyle: { color: '#e5e7eb' } },
      axisLabel: { color: '#6b7280' }
    },
    yAxis: {
      type: 'value',
      axisLine: { lineStyle: { color: '#e5e7eb' } },
      axisTick: { lineStyle: { color: '#e5e7eb' } },
      axisLabel: { color: '#6b7280' },
      splitLine: { lineStyle: { color: '#f3f4f6' } }
    },
    series: [{
      type: 'bar',
      data: values,
      itemStyle: {
        color: '#3b82f6',
        borderRadius: [4, 4, 0, 0]
      },
      emphasis: {
        itemStyle: { 
          color: '#2563eb',
          shadowBlur: 10,
          shadowColor: 'rgba(59, 130, 246, 0.3)'
        }
      },
      cursor: 'pointer'
    }]
  }
}

const createPieChartOption = (data) => {
  return {
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        return `${params.name}<br/>${params.seriesName}: ${params.value} (${params.percent}%)<br/><span style="color: #6b7280; font-size: 12px;">点击可下钻分析</span>`
      },
      backgroundColor: '#fff',
      borderColor: '#e5e7eb',
      borderWidth: 1,
      textStyle: { color: '#1f2937' }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: { color: '#6b7280' }
    },
    series: [{
      name: chartTitle.value,
      type: 'pie',
      radius: '50%',
      center: ['60%', '50%'],
      data: data.map(item => ({
        value: item.metricValue,
        name: item.value
      })),
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      cursor: 'pointer',
      itemStyle: {
        borderWidth: 2,
        borderColor: '#fff'
      }
    }]
  }
}

const createLineChartOption = (labels, values) => {
  return {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: labels
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      type: 'line',
      data: values,
      smooth: true,
      lineStyle: { color: '#3b82f6' },
      itemStyle: { color: '#3b82f6' },
      areaStyle: { color: 'rgba(59, 130, 246, 0.1)' }
    }]
  }
}

const updateTableData = () => {
  console.log('更新表格数据:', statisticsResult.value?.detailedData)
  if (!statisticsResult.value?.detailedData) {
    tableData.value = []
    return
  }
  
  // 直接赋值详细数据到表格数据源
  tableData.value = statisticsResult.value.detailedData
  console.log('表格数据已更新，共', tableData.value.length, '条记录')
  
  // 更新分页信息
  if (statisticsResult.value?.pagination) {
    pagination.total = statisticsResult.value.pagination.totalCount || tableData.value.length
    console.log('分页信息已更新，总记录数:', pagination.total)
  }
}

const getColumnLabel = (key) => {
  const labelMap = {
    id: 'ID',
    assetCode: '资产编号',
    assetName: '资产名称',
    assetType: '资产类型',
    department: '部门',
    location: '位置',
    status: '状态',
    price: '价值(万元)',
    createdAt: '创建时间'
  }
  return labelMap[key] || key
}

const getColumnWidth = (key) => {
  const widthMap = {
    id: 80,
    assetCode: 120,
    assetName: 150,
    status: 100,
    price: 120
  }
  return widthMap[key] || undefined
}

const getColumnFormatter = (key) => {
  if (key === 'price') {
    return (row) => row[key] ? `¥${Number(row[key]).toFixed(2)}万` : '-'
  }
  if (key === 'createdAt') {
    return (row) => row[key] ? new Date(row[key]).toLocaleDateString() : '-'
  }
  return undefined
}

// 事件处理方法
const handleDimensionChange = () => {
  executeQuery()
}

const handleMetricChange = () => {
  executeQuery()
}

const handleStatusChange = () => {
  executeQuery()
}

const handleFilterChange = () => {
  executeQuery()
}

const handleDateRangeChange = () => {
  queryParams.dateRange = dateRange.value
  executeQuery()
}

const handleChartClick = (params) => {
  // 实现图表点击下钻功能
  const clickedValue = params.name
  const dimension = queryParams.dimension
  
  // 记录下钻历史
  drillDownHistory.value.push({
    dimension: dimension,
    value: clickedValue,
    filters: { ...queryParams.filters }
  })
  
  // 设置筛选条件
  queryParams.filters[dimension] = clickedValue
  
  // 自动切换到合适的下级维度
  switchToDetailDimension(dimension)
  
  executeQuery()
  
  ElMessage.success(`已下钻至 ${getDimensionName(dimension)}: ${clickedValue}`)
}

const switchToDetailDimension = (currentDimension) => {
  // 定义维度下钻路径
  const drillPath = {
    'department': 'assetType',
    'assetType': 'location',
    'location': 'status',
    'status': 'department'
  }

  const nextDimension = drillPath[currentDimension]
  if (nextDimension) {
    queryParams.dimension = nextDimension
  }
}

const getDimensionName = (dimension) => {
  const dimensionMap = {
    'department': '部门',
    'assetType': '资产类型',
    'location': '区域',
    'status': '状态'
  }
  return dimensionMap[dimension] || dimension
}

// 返回上一级功能
const goBackToPreviousLevel = () => {
  if (drillDownHistory.value.length === 0) return
  
  const previousLevel = drillDownHistory.value.pop()
  queryParams.dimension = previousLevel.dimension
  queryParams.filters = { ...previousLevel.filters }
  
  executeQuery()
  
  ElMessage.info('已返回上一级')
}

// 重置所有下钻
const resetDrillDown = () => {
  drillDownHistory.value = []
  queryParams.filters = {}
  queryParams.dimension = 'department' // 重置到默认维度
  executeQuery()
}

// 回到指定层级
const backToLevel = (levelIndex) => {
  // 截取历史到指定层级
  const targetLevel = drillDownHistory.value[levelIndex]
  drillDownHistory.value = drillDownHistory.value.slice(0, levelIndex)
  
  // 恢复该层级的状态
  queryParams.dimension = targetLevel.dimension
  queryParams.filters = { ...targetLevel.filters }
  
  executeQuery()
  
  ElMessage.info(`已返回到 ${getDimensionName(targetLevel.dimension)} 层级`)
}

const handleRowClick = (row) => {
  // 实现行点击事件
  console.log('点击行:', row)
}

const changeChartType = (type) => {
  chartType.value = type
  updateChart()
}

const handleSizeChange = (val) => {
  pagination.pageSize = val
  queryParams.pagination.size = val
  executeQuery()
}

const handleCurrentChange = (val) => {
  pagination.currentPage = val
  queryParams.pagination.page = val
  executeQuery()
}

const resetFilters = () => {
  queryParams.dimension = 'department'
  queryParams.metric = 'count'
  queryParams.filters = {}
  selectedStatuses.value = ['在用', '闲置', '维修中', '报废']
  dateRange.value = []
  pagination.currentPage = 1
  pagination.pageSize = 20
  
  executeQuery()
  
  ElMessage.success('筛选条件已重置')
}

const exportData = () => {
  ElMessageBox.confirm('确定要导出当前数据吗？', '确认导出', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    // 实现数据导出逻辑
    ElMessage.success('数据导出功能开发中...')
  }).catch(() => {
    // 用户取消
  })
}

// 主题切换
const toggleTheme = () => {
  isDarkTheme.value = !isDarkTheme.value
  document.body.classList.toggle('dark-theme', isDarkTheme.value)
  document.body.classList.toggle('light-theme', !isDarkTheme.value)
  
  // 重新渲染所有图表
  nextTick(() => {
    if (factoryMapInstance.value) {
      factoryMapInstance.value.dispose()
      initFactoryMap()
    }
    
    if (timeSeriesInstance.value) {
      timeSeriesInstance.value.dispose()
      initTimeSeriesChart()
    }
    
    if (chartInstance.value) {
      chartInstance.value.dispose()
      updateChart()
    }
    
    if (valueDistributionInstance.value) {
      valueDistributionInstance.value.dispose()
      initValueDistributionChart()
    }
    
    if (matrixInstance.value) {
      matrixInstance.value.dispose()
      initMatrixChart()
    }
    
    if (statusDistributionInstance.value) {
      statusDistributionInstance.value.dispose()
      initStatusDistributionChart()
    }
  })
}

// 区域切换
const handleAreaChange = () => {
  selectedAreaInfo.value = null
  initFactoryMap()
  initTimeSeriesChart()
  initValueDistributionChart()
  initStatusDistributionChart()
}

// 初始化厂区地图
const initFactoryMap = () => {
  if (!factoryMap.value) return
  
  factoryMapInstance.value = echarts.init(factoryMap.value, isDarkTheme.value ? 'dark' : null)
  
  // 获取当前选择的区域数据
  const currentAreaData = factoryAreas[selectedArea.value] || factoryAreas['生产车间']
  
  // 构建地图数据
  const mapData = currentAreaData.map(area => ({
    name: area.name,
    value: area.value,
    coords: area.coords,
    assetCount: area.assetCount,
    onlineRate: area.onlineRate,
    utilizationRate: area.utilizationRate,
    assetTypes: area.assetTypes,
    valueDistribution: area.valueDistribution,
    maintenanceStatus: area.maintenanceStatus
  }))
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: params => {
        return `
          <div class="tooltip-content">
            <div class="tooltip-title">${params.name}</div>
            <div>资产数量: ${params.data.assetCount}台</div>
            <div>在线率: ${params.data.onlineRate}%</div>
            <div>利用率: ${params.data.utilizationRate}%</div>
          </div>
        `
      }
    },
    series: [
      {
        type: 'custom',
        coordinateSystem: 'none',
        renderItem: (params, api) => {
          const areaData = api.value(0)
          if (!areaData) return
          
          const coords = areaData.coords
          if (!coords || !coords.length) return
          
          // 构建多边形路径
          const points = coords.map(coord => [coord[0], coord[1]])
          
          // 获取热力图颜色
          const color = getHeatColor(areaData.value, isDarkTheme.value)
          
          // 构建多边形形状
          return {
            type: 'polygon',
            shape: {
              points: points
            },
            style: {
              fill: color,
              stroke: isDarkTheme.value ? 'rgba(255, 255, 255, 0.25)' : 'rgba(0, 0, 0, 0.25)',
              lineWidth: 1
            },
            // 添加点击交互
            emphasis: {
              style: {
                fill: isDarkTheme.value ? 'rgba(0, 255, 255, 0.8)' : 'rgba(24, 144, 255, 0.8)',
                stroke: isDarkTheme.value ? '#fff' : '#000',
                lineWidth: 2
              }
            },
            // 添加标签
            textContent: {
              style: {
                text: areaData.name,
                fill: isDarkTheme.value ? '#fff' : '#000',
                fontSize: 12,
                fontWeight: 'bold'
              }
            },
            textConfig: {
              position: 'inside'
            }
          }
        },
        data: mapData,
        // 添加点击事件
        emphasis: {
          focus: 'self'
        },
        // 添加选中状态
        select: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: isDarkTheme.value ? 'rgba(0, 255, 255, 0.5)' : 'rgba(24, 144, 255, 0.5)'
          }
        }
      }
    ]
  }
  
  factoryMapInstance.value.setOption(option)
  
  // 添加点击事件
  factoryMapInstance.value.on('click', params => {
    const areaData = currentAreaData.find(area => area.name === params.name)
    if (areaData) {
      selectedAreaInfo.value = areaData
      // 更新时段分析图表
      updateTimeSeriesChart(selectedArea.value, params.name)
      // 更新资产价值分布图
      updateValueDistributionChart(areaData.valueDistribution)
      // 更新资产状态分布图
      updateStatusDistributionChart(areaData.maintenanceStatus)
    }
  })
  
  // 默认选中第一个区域
  if (currentAreaData.length > 0 && !selectedAreaInfo.value) {
    selectedAreaInfo.value = currentAreaData[0]
    updateTimeSeriesChart(selectedArea.value, currentAreaData[0].name)
  }
}

// 更新时段分析图表
const updateTimeSeriesChart = (areaType, areaName) => {
  if (!timeSeriesInstance.value) return
  
  // 获取区域数据
  const areaData = timeSeriesData.areas[areaType] || {
    workday: timeSeriesData.workday,
    weekend: timeSeriesData.weekend
  }
  
  timeSeriesInstance.value.setOption({
    title: {
      text: `${areaName} - 设备使用时段分析`,
    },
    series: [
      {
        data: areaData.workday
      },
      {
        data: areaData.weekend
      }
    ]
  })
}

// 初始化时间序列图表
const initTimeSeriesChart = () => {
  if (!timeSeriesChart.value) return

  timeSeriesInstance.value = echarts.init(timeSeriesChart.value, isDarkTheme.value ? 'dark' : null)

  const option = {
    title: {
      text: '设备使用时段分析',
      textStyle: {
        color: isDarkTheme.value ? '#e2e8f0' : '#1f2937',
        fontSize: 16
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: isDarkTheme.value ? '#374151' : '#ffffff',
      borderColor: isDarkTheme.value ? '#6b7280' : '#e5e7eb',
      textStyle: {
        color: isDarkTheme.value ? '#e2e8f0' : '#1f2937'
      }
    },
    legend: {
      data: ['在线设备', '离线设备', '维护中'],
      textStyle: {
        color: isDarkTheme.value ? '#e2e8f0' : '#1f2937'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
      axisLine: {
        lineStyle: {
          color: isDarkTheme.value ? '#6b7280' : '#e5e7eb'
        }
      },
      axisLabel: {
        color: isDarkTheme.value ? '#9ca3af' : '#6b7280'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: isDarkTheme.value ? '#6b7280' : '#e5e7eb'
        }
      },
      axisLabel: {
        color: isDarkTheme.value ? '#9ca3af' : '#6b7280'
      },
      splitLine: {
        lineStyle: {
          color: isDarkTheme.value ? '#374151' : '#f3f4f6'
        }
      }
    },
    series: [
      {
        name: '在线设备',
        type: 'line',
        data: [85, 82, 88, 92, 89, 86],
        smooth: true,
        lineStyle: {
          color: '#10b981'
        },
        itemStyle: {
          color: '#10b981'
        },
        areaStyle: {
          color: 'rgba(16, 185, 129, 0.1)'
        }
      },
      {
        name: '离线设备',
        type: 'line',
        data: [12, 15, 10, 6, 8, 11],
        smooth: true,
        lineStyle: {
          color: '#ef4444'
        },
        itemStyle: {
          color: '#ef4444'
        },
        areaStyle: {
          color: 'rgba(239, 68, 68, 0.1)'
        }
      },
      {
        name: '维护中',
        type: 'line',
        data: [3, 3, 2, 2, 3, 3],
        smooth: true,
        lineStyle: {
          color: '#f59e0b'
        },
        itemStyle: {
          color: '#f59e0b'
        },
        areaStyle: {
          color: 'rgba(245, 158, 11, 0.1)'
        }
      }
    ]
  }

  timeSeriesInstance.value.setOption(option)
}

// 初始化资产价值分布图表
const initValueDistributionChart = () => {
  if (!valueDistributionChart.value) return
  
  valueDistributionInstance.value = echarts.init(valueDistributionChart.value, isDarkTheme.value ? 'dark' : null)
  
  const option = {
    title: {
      text: '资产价值区间分布',
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: isDarkTheme.value ? '#eee' : '#333'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: assetValueDistribution.data.map(item => item.name),
      bottom: '0%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: assetValueDistribution.ranges,
      axisLabel: {
        interval: 0,
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      name: '资产数量',
      nameTextStyle: {
        padding: [0, 0, 0, 30]
      }
    },
    series: assetValueDistribution.data.map(item => ({
      name: item.name,
      type: 'bar',
      stack: '总量',
      emphasis: {
        focus: 'series'
      },
      data: item.values
    }))
  }
  
  valueDistributionInstance.value.setOption(option)
}

// 更新资产价值分布图表
const updateValueDistributionChart = (valueData) => {
  if (!valueDistributionInstance.value || !valueData) return
  
  const data = valueData.map(item => item.count)
  
  valueDistributionInstance.value.setOption({
    series: [
      {
        data: data,
        type: 'bar',
        name: '资产数量'
      }
    ],
    legend: {
      data: ['资产数量']
    }
  })
}

// 初始化部门资产矩阵图
const initMatrixChart = () => {
  if (!matrixChart.value) return
  
  matrixInstance.value = echarts.init(matrixChart.value, isDarkTheme.value ? 'dark' : null)
  
  const option = {
    title: {
      text: '部门资产矩阵',
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: isDarkTheme.value ? '#eee' : '#333'
      }
    },
    tooltip: {
      position: 'top'
    },
    grid: {
      left: '3%',
      right: '7%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: departmentAssetMatrix.assetTypes,
      splitArea: {
        show: true
      },
      axisLabel: {
        interval: 0,
        fontSize: 10,
        rotate: 30
      }
    },
    yAxis: {
      type: 'category',
      data: departmentAssetMatrix.departments,
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: 0,
      max: 150,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '0%',
      inRange: {
        color: isDarkTheme.value 
          ? ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffbf', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
          : ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffbf', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026'].reverse()
      }
    },
    series: [{
      name: '资产数量',
      type: 'heatmap',
      data: departmentAssetMatrix.data.flatMap((row, i) => 
        row.map((val, j) => [j, i, val])
      ),
      label: {
        show: true
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  
  matrixInstance.value.setOption(option)
}

// 初始化资产状态分布图
const initStatusDistributionChart = () => {
  if (!statusDistributionChart.value) return
  
  statusDistributionInstance.value = echarts.init(statusDistributionChart.value, isDarkTheme.value ? 'dark' : null)
  
  const option = {
    title: {
      text: '资产状态分布',
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: isDarkTheme.value ? '#eee' : '#333'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: '0%',
      data: assetStatusDistribution.statuses
    },
    series: [
      {
        name: '资产状态',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: isDarkTheme.value ? '#0f172a' : '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: assetStatusDistribution.statuses.map((status, index) => ({
          value: assetStatusDistribution.data[0][index],
          name: status
        }))
      }
    ]
  }
  
  statusDistributionInstance.value.setOption(option)
}

// 更新资产状态分布图
const updateStatusDistributionChart = (statusData) => {
  if (!statusDistributionInstance.value || !statusData) return
  
  const data = Object.entries(statusData).map(([name, value]) => ({
    name,
    value
  }))
  
  statusDistributionInstance.value.setOption({
    series: [
      {
        data: data
      }
    ],
    legend: {
      data: Object.keys(statusData)
    }
  })
}

// 处理资产类型选择
const handleAssetTypeChange = (type) => {
  selectedAssetType.value = type
  
  // 更新相关图表
  if (type !== '全部') {
    const typeData = timeSeriesData.assetTypes[type]
    if (typeData) {
      updateTimeSeriesChart(type, type)
    }
  } else {
    updateTimeSeriesChart(selectedArea.value, selectedAreaInfo.value?.name || selectedArea.value)
  }
}

// 组件卸载前清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  
  // 销毁图表实例
  if (chartInstance.value) chartInstance.value.dispose()
  if (factoryMapInstance.value) factoryMapInstance.value.dispose()
  if (timeSeriesInstance.value) timeSeriesInstance.value.dispose()
  if (valueDistributionInstance.value) valueDistributionInstance.value.dispose()
  if (matrixInstance.value) matrixInstance.value.dispose()
  if (statusDistributionInstance.value) statusDistributionInstance.value.dispose()
})

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance.value) chartInstance.value.resize()
  if (factoryMapInstance.value) factoryMapInstance.value.resize()
  if (timeSeriesInstance.value) timeSeriesInstance.value.resize()
  if (valueDistributionInstance.value) valueDistributionInstance.value.resize()
  if (matrixInstance.value) matrixInstance.value.resize()
  if (statusDistributionInstance.value) statusDistributionInstance.value.resize()
}
</script>

<style scoped>
.analytics-workbench {
  display: flex;
  width: 100%;
  height: calc(100vh - 64px);
  overflow: hidden;
  background-color: var(--bg-color);
  color: var(--text-color);
}

/* 主题变量 */
.dark-theme {
  --bg-color: #0f172a;
  --card-bg: #1e293b;
  --text-color: #e2e8f0;
  --border-color: rgba(255, 255, 255, 0.1);
  --hover-color: rgba(255, 255, 255, 0.05);
  --shadow-color: rgba(0, 0, 0, 0.5);
  --header-bg: rgba(30, 41, 59, 0.8);
  --accent-color: #3b82f6;
  --accent-hover: #2563eb;
}

.light-theme {
  --bg-color: #f8fafc;
  --card-bg: #ffffff;
  --text-color: #1e293b;
  --border-color: rgba(0, 0, 0, 0.1);
  --hover-color: rgba(0, 0, 0, 0.02);
  --shadow-color: rgba(0, 0, 0, 0.1);
  --header-bg: rgba(255, 255, 255, 0.9);
  --accent-color: #3b82f6;
  --accent-hover: #2563eb;
}

/* 侧边控制面板 */
.control-panel {
  width: 300px;
  min-width: 300px;
  height: 100%;
  overflow-y: auto;
  background-color: var(--card-bg);
  border-right: 1px solid var(--border-color);
  padding: 20px 0;
  box-shadow: 2px 0 10px var(--shadow-color);
  z-index: 10;
}

/* 面板头部 */
.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px 20px;
  border-bottom: 1px solid var(--border-color);
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo-icon {
  font-size: 24px;
  color: var(--accent-color);
  margin-right: 10px;
}

.panel-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: var(--text-color);
}

.theme-toggle {
  font-size: 20px;
  color: var(--text-color);
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s;
}

.theme-toggle:hover {
  background-color: var(--hover-color);
}

/* 筛选面板 */
.filters-panel {
  padding: 20px;
}

.filter-group {
  margin-bottom: 20px;
}

.filter-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-color);
}

.filter-select {
  width: 100%;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.reset-btn {
  width: 100%;
  margin-top: 10px;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: var(--bg-color);
}

/* 页面头部 */
.content-header {
  margin-bottom: 20px;
}

.dashboard-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px;
  color: var(--text-color);
}

.dashboard-subtitle {
  font-size: 14px;
  color: var(--text-color);
  opacity: 0.8;
  margin: 0;
}

/* KPI指标卡片 */
.kpi-section {
  margin-bottom: 20px;
}

.kpi-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.kpi-card {
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 4px 6px var(--shadow-color);
  text-align: center;
  transition: all 0.3s;
}

.kpi-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px var(--shadow-color);
}

.kpi-label {
  font-size: 14px;
  color: var(--text-color);
  opacity: 0.8;
  margin: 0 0 5px;
}

.kpi-value {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.kpi-unit {
  font-size: 14px;
  opacity: 0.7;
}

.kpi-value-default {
  color: var(--text-color);
}

.kpi-value-success {
  color: #10b981;
}

.kpi-value-warning {
  color: #f59e0b;
}

.kpi-value-danger {
  color: #ef4444;
}

/* 调试信息 */
.debug-section {
  margin-bottom: 20px;
}

.debug-card {
  background-color: var(--card-bg);
  border-radius: 8px;
  overflow: hidden;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.debug-title {
  margin: 0;
  font-size: 16px;
}

.debug-content {
  max-height: 400px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 12px;
  white-space: pre-wrap;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

/* 网格布局 */
.grid-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.second-row {
  grid-template-columns: 1fr 1fr;
}

/* 左侧列 */
.left-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 右侧列 */
.right-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 卡片样式 */
:deep(.el-card) {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 4px 6px var(--shadow-color);
  transition: all 0.3s;
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.el-card:hover) {
  box-shadow: 0 8px 15px var(--shadow-color);
}

:deep(.el-card__header) {
  padding: 15px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--header-bg);
}

:deep(.el-card__body) {
  padding: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 卡片头部 */
.card-header, .chart-header, .table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3, .chart-header h3, .table-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
}

.header-actions {
  display: flex;
  gap: 10px;
}

/* 厂区地图 */
.map-card {
  flex: 1;
}

.factory-map-container {
  position: relative;
  width: 100%;
  height: 300px;
}

.factory-map {
  width: 100%;
  height: 100%;
}

.map-legend {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background-color: var(--card-bg);
  padding: 5px 10px;
  border-radius: 4px;
  box-shadow: 0 2px 4px var(--shadow-color);
  z-index: 1;
}

.legend-title {
  font-size: 12px;
  margin-bottom: 5px;
  color: var(--text-color);
}

.legend-gradient {
  height: 10px;
  width: 100px;
  background: linear-gradient(to right, 
    rgba(0, 32, 96, 0.6), 
    rgba(0, 64, 128, 0.7), 
    rgba(0, 128, 192, 0.8), 
    rgba(0, 192, 255, 0.9), 
    rgba(64, 224, 255, 1.0)
  );
  border-radius: 2px;
  margin-bottom: 2px;
}

.dark-theme .legend-gradient {
  background: linear-gradient(to right, 
    rgba(0, 32, 96, 0.6), 
    rgba(0, 64, 128, 0.7), 
    rgba(0, 128, 192, 0.8), 
    rgba(0, 192, 255, 0.9), 
    rgba(64, 224, 255, 1.0)
  );
}

.light-theme .legend-gradient {
  background: linear-gradient(to right, 
    rgba(240, 249, 255, 0.8), 
    rgba(189, 229, 255, 0.8), 
    rgba(107, 174, 214, 0.8), 
    rgba(49, 130, 189, 0.8), 
    rgba(8, 81, 156, 0.8)
  );
}

.legend-labels {
  display: flex;
  justify-content: space-between;
  font-size: 10px;
  color: var(--text-color);
}

.area-info {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: var(--card-bg);
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 4px var(--shadow-color);
  max-width: 200px;
  z-index: 1;
}

.area-info h4 {
  margin: 0 0 10px;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
}

.area-stats {
  font-size: 12px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.stat-label {
  color: var(--text-color);
  opacity: 0.8;
}

.stat-value {
  font-weight: 500;
  color: var(--text-color);
}

.asset-types {
  margin-top: 10px;
  border-top: 1px solid var(--border-color);
  padding-top: 5px;
}

.asset-types-title {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 5px;
  color: var(--text-color);
}

.asset-type-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
  font-size: 11px;
}

.asset-type-label {
  color: var(--text-color);
  opacity: 0.8;
}

.asset-type-value {
  font-weight: 500;
  color: var(--text-color);
}

/* 时段分析图表 */
.time-series-card {
  flex: 1;
}

.time-series-chart {
  width: 100%;
  height: 250px;
}

/* 统计卡片 */
.stats-card {
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.stat-item {
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  transition: all 0.3s;
}

.stat-item:hover {
  transform: translateY(-3px);
}

.stat-item p {
  margin: 0 0 5px;
  font-size: 14px;
  opacity: 0.8;
}

.stat-item h3 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.stat-item.online {
  background-color: rgba(59, 130, 246, 0.2);
}

.stat-item.idle {
  background-color: rgba(16, 185, 129, 0.2);
}

.stat-item.maintenance {
  background-color: rgba(245, 158, 11, 0.2);
}

.stat-item.utilization {
  background-color: rgba(139, 92, 246, 0.2);
}

/* 图表卡片 */
.chart-card {
  margin-bottom: 20px;
  flex: 1;
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 250px;
}

.chart-canvas {
  width: 100%;
  height: 100%;
  min-height: 250px;
}

/* 表格区域 */
.table-section {
  margin-bottom: 20px;
}

.table-card {
  background-color: var(--card-bg);
}

.table-header {
  margin-bottom: 0;
}

.table-summary {
  display: flex;
  align-items: center;
  gap: 10px;
}

.summary-text {
  font-size: 14px;
  color: var(--text-color);
  opacity: 0.8;
}

.data-table {
  width: 100%;
  margin-bottom: 15px;
}

:deep(.el-table) {
  background-color: var(--card-bg);
  color: var(--text-color);
}

:deep(.el-table th) {
  background-color: var(--header-bg);
  color: var(--text-color);
  font-weight: 600;
}

:deep(.el-table td), :deep(.el-table th.is-leaf) {
  border-bottom: 1px solid var(--border-color);
}

:deep(.el-table--border), :deep(.el-table--group) {
  border: 1px solid var(--border-color);
}

:deep(.el-table--border .el-table__cell) {
  border-right: 1px solid var(--border-color);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: var(--hover-color);
}

:deep(.el-table__body tr.hover-row > td) {
  background-color: var(--hover-color);
}

.empty-data {
  padding: 40px 0;
  text-align: center;
  color: var(--text-color);
  opacity: 0.5;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding-top: 15px;
}

:deep(.el-pagination) {
  color: var(--text-color);
}

:deep(.el-pagination .el-pagination__total),
:deep(.el-pagination .el-pagination__jump) {
  color: var(--text-color);
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  background-color: var(--card-bg);
  color: var(--text-color);
}

:deep(.el-pagination .el-pager li) {
  background-color: var(--card-bg);
  color: var(--text-color);
}

:deep(.el-pagination .el-pager li.active) {
  color: var(--accent-color);
  font-weight: bold;
}

:deep(.el-pagination .el-select .el-input) {
  color: var(--text-color);
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .grid-layout {
    grid-template-columns: 1fr;
  }
  
  .second-row {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .analytics-workbench {
    flex-direction: column;
    height: auto;
  }
  
  .control-panel {
    width: 100%;
    height: auto;
    max-height: 300px;
  }
  
  .kpi-container {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>