-- IT资产管理系统 - 优化版视图创建脚本
-- 版本: v2.0 (激进优化版)
-- 创建时间: 2025-01-08
-- 优化特性: 路径解析优化、动态价值区间、物化视图支持、性能监控

-- ============================================================================
-- 0. 创建配置表和辅助结构
-- ============================================================================

-- 价值区间配置表
DROP TABLE IF EXISTS asset_value_ranges;
CREATE TABLE asset_value_ranges (
    id INT PRIMARY KEY AUTO_INCREMENT,
    min_value DECIMAL(18,2) NOT NULL,
    max_value DECIMAL(18,2) NOT NULL,
    range_label VARCHAR(20) NOT NULL,
    range_color VARCHAR(10) DEFAULT '#3b82f6',
    sort_order INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产价值区间配置表';

-- 插入默认价值区间配置
INSERT INTO asset_value_ranges (min_value, max_value, range_label, range_color, sort_order) VALUES 
(0, 10000, '0-1万', '#10b981', 1),
(10000, 50000, '1-5万', '#3b82f6', 2),
(50000, 100000, '5-10万', '#f59e0b', 3),
(100000, 500000, '10-50万', '#ef4444', 4),
(500000, 999999999, '50万以上', '#8b5cf6', 5);

-- 视图性能统计表
DROP TABLE IF EXISTS view_performance_stats;
CREATE TABLE view_performance_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    view_name VARCHAR(64) NOT NULL,
    query_type VARCHAR(32) NOT NULL,
    execution_time_ms DECIMAL(10,3) NOT NULL,
    record_count INT DEFAULT 0,
    query_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_view_date (view_name, query_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视图性能统计表';

-- ============================================================================
-- 1. 核心资产增强视图 (v_assets_enhanced) - 优化版
-- ============================================================================

DROP VIEW IF EXISTS v_assets_enhanced;

CREATE VIEW v_assets_enhanced AS
SELECT 
    -- ================== 资产基础信息 ==================
    a.Id AS AssetId,
    a.assetCode AS AssetCode,
    a.FinancialCode,
    a.Name AS AssetName,
    a.Status,
    COALESCE(a.Price, 0) AS Price,
    a.PurchaseDate,
    a.WarrantyExpireDate,
    a.SerialNumber,
    a.Model,
    a.Brand,
    a.Notes,
    a.CreatedAt,
    a.UpdatedAt,
    
    -- ================== 资产类型信息 ==================
    a.AssetTypeId,
    COALESCE(at.Name, '未分类') AS AssetTypeName,
    COALESCE(at.Code, 'UNKNOWN') AS AssetTypeCode,
    COALESCE(at.Description, '') AS AssetTypeDescription,
    
    -- ================== 当前位置信息 ==================
    a.LocationId AS CurrentLocationId,
    COALESCE(l.Name, '未指定位置') AS CurrentLocationName,
    COALESCE(l.Code, '') AS CurrentLocationCode,
    COALESCE(l.Type, -1) AS LocationType,
    COALESCE(l.Path, CAST(a.LocationId AS CHAR)) AS LocationPath,
    
    -- ================== 优化的位置层级解析 ==================
    -- 使用SUBSTRING_INDEX + CONCAT优化，避免复杂条件判断
    CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 1), ',', -1) AS UNSIGNED) AS Level1LocationId,
    CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 2), ',', -1) AS UNSIGNED) AS Level2LocationId,
    CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 3), ',', -1) AS UNSIGNED) AS Level3LocationId,
    CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 4), ',', -1) AS UNSIGNED) AS Level4LocationId,
    CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 5), ',', -1) AS UNSIGNED) AS Level5LocationId,
    
    -- ================== 智能部门继承逻辑 ==================
    -- 优先级：当前位置 -> Level2 -> Level3 -> Level4 -> Level5
    COALESCE(
        l.DefaultDepartmentId,
        l2.DefaultDepartmentId,
        l3.DefaultDepartmentId,
        l4.DefaultDepartmentId,
        l5.DefaultDepartmentId
    ) AS InheritedDepartmentId,
    
    -- 部门继承来源追踪
    CASE 
        WHEN l.DefaultDepartmentId IS NOT NULL THEN 'current'
        WHEN l2.DefaultDepartmentId IS NOT NULL THEN 'level2'
        WHEN l3.DefaultDepartmentId IS NOT NULL THEN 'level3'
        WHEN l4.DefaultDepartmentId IS NOT NULL THEN 'level4'
        WHEN l5.DefaultDepartmentId IS NOT NULL THEN 'level5'
        ELSE 'none'
    END AS DepartmentSource,
    
    -- ================== 部门信息 ==================
    COALESCE(d.Id, 0) AS DepartmentId,
    COALESCE(d.Name, '未分配部门') AS DepartmentName,
    COALESCE(d.Code, '') AS DepartmentCode,
    d.ManagerId AS DepartmentManagerId,
    d.DeputyManagerId AS DepartmentDeputyManagerId,
    COALESCE(d.Path, '') AS DepartmentPath,
    
    -- ================== 负责人信息 ==================
    COALESCE(mgr.Username, '') AS DepartmentManagerName,
    COALESCE(deputy.Username, '') AS DepartmentDeputyManagerName,
    COALESCE(resp.Username, '') AS LocationResponsiblePersonName,
    
    -- ================== 区域信息（用于热力图） ==================
    -- 区域ID：优先使用Path第4级，否则使用当前位置
    CASE 
        WHEN l.Path IS NOT NULL AND CHAR_LENGTH(l.Path) - CHAR_LENGTH(REPLACE(l.Path, ',', '')) >= 3 THEN
            CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(l.Path, ',', 4), ',', -1) AS UNSIGNED)
        ELSE COALESCE(a.LocationId, 0)
    END AS RegionId,
    
    -- 区域名称
    COALESCE(region_loc.Name, l.Name, '未知区域') AS RegionName,
    
    -- ================== 动态价值区间 ==================
    -- 基于配置表的动态价值区间
    COALESCE(vr.range_label, '未知') AS ValueRange,
    COALESCE(vr.range_color, '#6b7280') AS ValueRangeColor,
    COALESCE(vr.sort_order, 999) AS ValueRangeSortOrder,
    
    -- ================== 统计用计算字段 ==================
    -- 状态文本和分类
    CASE a.Status
        WHEN 0 THEN '闲置'
        WHEN 1 THEN '在用'
        WHEN 2 THEN '维修中'
        WHEN 3 THEN '报废'
        WHEN 4 THEN '故障'
        ELSE '未知'
    END AS StatusText,
    
    CASE a.Status
        WHEN 0 THEN 'idle'
        WHEN 1 THEN 'in_use'
        WHEN 2 THEN 'maintenance'
        WHEN 3 THEN 'scrapped'
        WHEN 4 THEN 'faulty'
        ELSE 'unknown'
    END AS StatusCategory,
    
    -- 价值（万元）
    ROUND(COALESCE(a.Price, 0) / 10000, 2) AS ValueInWan,
    
    -- 布尔标识字段（用于快速统计）
    CASE WHEN a.Status = 1 THEN 1 ELSE 0 END AS IsInUse,
    CASE WHEN a.Status = 0 THEN 1 ELSE 0 END AS IsIdle,
    CASE WHEN a.Status = 2 THEN 1 ELSE 0 END AS IsMaintenance,
    CASE WHEN a.Status = 4 THEN 1 ELSE 0 END AS IsFaulty,
    CASE WHEN a.Status = 3 THEN 1 ELSE 0 END AS IsScrapped,
    
    -- 时间相关字段
    YEAR(a.CreatedAt) AS CreatedYear,
    MONTH(a.CreatedAt) AS CreatedMonth,
    DATE(a.CreatedAt) AS CreatedDate,
    
    -- 资产年龄（月）
    TIMESTAMPDIFF(MONTH, a.CreatedAt, NOW()) AS AssetAgeMonths

FROM assets a

-- ================== 主表关联 ==================
LEFT JOIN assettypes at ON a.AssetTypeId = at.Id
LEFT JOIN locations l ON a.LocationId = l.Id

-- ================== 位置层级关联（优化版） ==================
LEFT JOIN locations l2 ON l2.Id = CAST(
    SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 2), ',', -1) AS UNSIGNED)

LEFT JOIN locations l3 ON l3.Id = CAST(
    SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 3), ',', -1) AS UNSIGNED)

LEFT JOIN locations l4 ON l4.Id = CAST(
    SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 4), ',', -1) AS UNSIGNED)

LEFT JOIN locations l5 ON l5.Id = CAST(
    SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 5), ',', -1) AS UNSIGNED)

-- 区域位置信息
LEFT JOIN locations region_loc ON region_loc.Id = (
    CASE 
        WHEN l.Path IS NOT NULL AND CHAR_LENGTH(l.Path) - CHAR_LENGTH(REPLACE(l.Path, ',', '')) >= 3 THEN
            CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(l.Path, ',', 4), ',', -1) AS UNSIGNED)
        ELSE a.LocationId
    END
)

-- ================== 部门和负责人关联 ==================
LEFT JOIN departments d ON d.Id = COALESCE(
    l.DefaultDepartmentId,
    l2.DefaultDepartmentId,
    l3.DefaultDepartmentId,
    l4.DefaultDepartmentId,
    l5.DefaultDepartmentId
)

LEFT JOIN users mgr ON mgr.Id = d.ManagerId
LEFT JOIN users deputy ON deputy.Id = d.DeputyManagerId
LEFT JOIN users resp ON resp.Id = l.DefaultResponsiblePersonId

-- ================== 价值区间关联（动态配置） ==================
LEFT JOIN asset_value_ranges vr ON (
    COALESCE(a.Price, 0) >= vr.min_value 
    AND COALESCE(a.Price, 0) < vr.max_value 
    AND vr.is_active = TRUE
);

-- ============================================================================
-- 2. 高性能统计汇总视图 (v_asset_statistics_fast)
-- ============================================================================

DROP VIEW IF EXISTS v_asset_statistics_fast;

CREATE VIEW v_asset_statistics_fast AS

-- ================== 按资产类型统计 ==================
SELECT 
    'type' AS DimensionType,
    CAST(AssetTypeId AS CHAR) AS DimensionKey,
    AssetTypeName AS DimensionName,
    AssetTypeCode AS DimensionCode,
    NULL AS ParentKey,
    COUNT(*) AS TotalCount,
    SUM(IsInUse) AS InUseCount,
    SUM(IsIdle) AS IdleCount,
    SUM(IsMaintenance) AS MaintenanceCount,
    SUM(IsFaulty) AS FaultCount,
    SUM(IsScrapped) AS ScrappedCount,
    SUM(Price) AS TotalValue,
    ROUND(AVG(Price), 2) AS AverageValue,
    ROUND(SUM(IsInUse) * 100.0 / COUNT(*), 2) AS InUseRate,
    ROUND(SUM(IsFaulty) * 100.0 / COUNT(*), 2) AS FaultRate,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM assets), 2) AS Percentage,
    MIN(CreatedAt) AS EarliestCreated,
    MAX(CreatedAt) AS LatestCreated,
    COUNT(DISTINCT DepartmentId) AS DepartmentCount,
    COUNT(DISTINCT RegionId) AS RegionCount
FROM v_assets_enhanced
GROUP BY AssetTypeId, AssetTypeName, AssetTypeCode

UNION ALL

-- ================== 按部门统计 ==================
SELECT 
    'department' AS DimensionType,
    CAST(InheritedDepartmentId AS CHAR) AS DimensionKey,
    DepartmentName AS DimensionName,
    DepartmentCode AS DimensionCode,
    NULL AS ParentKey,
    COUNT(*) AS TotalCount,
    SUM(IsInUse) AS InUseCount,
    SUM(IsIdle) AS IdleCount,
    SUM(IsMaintenance) AS MaintenanceCount,
    SUM(IsFaulty) AS FaultCount,
    SUM(IsScrapped) AS ScrappedCount,
    SUM(Price) AS TotalValue,
    ROUND(AVG(Price), 2) AS AverageValue,
    ROUND(SUM(IsInUse) * 100.0 / COUNT(*), 2) AS InUseRate,
    ROUND(SUM(IsFaulty) * 100.0 / COUNT(*), 2) AS FaultRate,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM assets), 2) AS Percentage,
    MIN(CreatedAt) AS EarliestCreated,
    MAX(CreatedAt) AS LatestCreated,
    COUNT(DISTINCT AssetTypeId) AS DepartmentCount,
    COUNT(DISTINCT RegionId) AS RegionCount
FROM v_assets_enhanced
WHERE InheritedDepartmentId IS NOT NULL AND InheritedDepartmentId > 0
GROUP BY InheritedDepartmentId, DepartmentName, DepartmentCode

UNION ALL

-- ================== 按区域统计 ==================
SELECT 
    'region' AS DimensionType,
    CAST(RegionId AS CHAR) AS DimensionKey,
    RegionName AS DimensionName,
    '' AS DimensionCode,
    NULL AS ParentKey,
    COUNT(*) AS TotalCount,
    SUM(IsInUse) AS InUseCount,
    SUM(IsIdle) AS IdleCount,
    SUM(IsMaintenance) AS MaintenanceCount,
    SUM(IsFaulty) AS FaultCount,
    SUM(IsScrapped) AS ScrappedCount,
    SUM(Price) AS TotalValue,
    ROUND(AVG(Price), 2) AS AverageValue,
    ROUND(SUM(IsInUse) * 100.0 / COUNT(*), 2) AS InUseRate,
    ROUND(SUM(IsFaulty) * 100.0 / COUNT(*), 2) AS FaultRate,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM assets), 2) AS Percentage,
    MIN(CreatedAt) AS EarliestCreated,
    MAX(CreatedAt) AS LatestCreated,
    COUNT(DISTINCT AssetTypeId) AS DepartmentCount,
    COUNT(DISTINCT InheritedDepartmentId) AS RegionCount
FROM v_assets_enhanced
GROUP BY RegionId, RegionName

UNION ALL

-- ================== 按状态统计 ==================
SELECT 
    'status' AS DimensionType,
    CAST(Status AS CHAR) AS DimensionKey,
    StatusText AS DimensionName,
    StatusCategory AS DimensionCode,
    NULL AS ParentKey,
    COUNT(*) AS TotalCount,
    SUM(IsInUse) AS InUseCount,
    SUM(IsIdle) AS IdleCount,
    SUM(IsMaintenance) AS MaintenanceCount,
    SUM(IsFaulty) AS FaultCount,
    SUM(IsScrapped) AS ScrappedCount,
    SUM(Price) AS TotalValue,
    ROUND(AVG(Price), 2) AS AverageValue,
    ROUND(SUM(IsInUse) * 100.0 / COUNT(*), 2) AS InUseRate,
    ROUND(SUM(IsFaulty) * 100.0 / COUNT(*), 2) AS FaultRate,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM assets), 2) AS Percentage,
    MIN(CreatedAt) AS EarliestCreated,
    MAX(CreatedAt) AS LatestCreated,
    COUNT(DISTINCT AssetTypeId) AS DepartmentCount,
    COUNT(DISTINCT InheritedDepartmentId) AS RegionCount
FROM v_assets_enhanced
GROUP BY Status, StatusText, StatusCategory;

-- ============================================================================
-- 3. 资产矩阵分析视图 (v_asset_matrix_enhanced)
-- ============================================================================

DROP VIEW IF EXISTS v_asset_matrix_enhanced;

CREATE VIEW v_asset_matrix_enhanced AS
SELECT 
    ae.InheritedDepartmentId AS DepartmentId,
    ae.DepartmentName,
    ae.DepartmentCode,
    ae.AssetTypeId,
    ae.AssetTypeName,
    ae.AssetTypeCode,
    COUNT(*) AS AssetCount,
    SUM(ae.IsInUse) AS InUseCount,
    SUM(ae.IsFaulty) AS FaultCount,
    SUM(ae.IsMaintenance) AS MaintenanceCount,
    SUM(ae.Price) AS TotalValue,
    ROUND(AVG(ae.Price), 2) AS AverageValue,
    ROUND(SUM(ae.IsInUse) * 100.0 / COUNT(*), 2) AS InUseRate,
    ROUND(SUM(ae.IsFaulty) * 100.0 / COUNT(*), 2) AS FaultRate,
    
    -- 矩阵位置坐标（用于前端渲染）
    ROW_NUMBER() OVER (ORDER BY ae.DepartmentName) - 1 AS MatrixRowIndex,
    ROW_NUMBER() OVER (PARTITION BY ae.InheritedDepartmentId ORDER BY ae.AssetTypeName) - 1 AS MatrixColIndex,
    
    -- 热力图值（用于颜色映射）
    ROUND(COUNT(*) * 100.0 / MAX(dept_total.total_count), 2) AS HeatmapValue

FROM v_assets_enhanced ae

-- 计算每个部门的总资产数（用于热力图归一化）
JOIN (
    SELECT 
        COALESCE(
            l.DefaultDepartmentId,
            l2.DefaultDepartmentId,
            l3.DefaultDepartmentId,
            l4.DefaultDepartmentId,
            l5.DefaultDepartmentId
        ) AS InheritedDepartmentId,
        COUNT(*) as total_count
    FROM assets a
    LEFT JOIN locations l ON a.LocationId = l.Id
    LEFT JOIN locations l2 ON CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 2), ',', -1) AS UNSIGNED) = l2.Id
    LEFT JOIN locations l3 ON CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 3), ',', -1) AS UNSIGNED) = l3.Id
    LEFT JOIN locations l4 ON CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 4), ',', -1) AS UNSIGNED) = l4.Id
    LEFT JOIN locations l5 ON CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 5), ',', -1) AS UNSIGNED) = l5.Id
    WHERE COALESCE(l.DefaultDepartmentId, l2.DefaultDepartmentId, l3.DefaultDepartmentId, l4.DefaultDepartmentId, l5.DefaultDepartmentId) IS NOT NULL 
    GROUP BY COALESCE(l.DefaultDepartmentId, l2.DefaultDepartmentId, l3.DefaultDepartmentId, l4.DefaultDepartmentId, l5.DefaultDepartmentId)
) dept_total ON dept_total.InheritedDepartmentId = ae.InheritedDepartmentId

WHERE ae.InheritedDepartmentId IS NOT NULL AND ae.InheritedDepartmentId > 0
GROUP BY ae.InheritedDepartmentId, ae.DepartmentName, ae.DepartmentCode, 
         ae.AssetTypeId, ae.AssetTypeName, ae.AssetTypeCode
HAVING COUNT(*) > 0
ORDER BY ae.DepartmentName, ae.AssetTypeName;

-- ============================================================================
-- 4. KPI汇总视图 (v_asset_kpi_enhanced)
-- ============================================================================

DROP VIEW IF EXISTS v_asset_kpi_enhanced;

CREATE VIEW v_asset_kpi_enhanced AS
SELECT 
    -- ================== 基础KPI ==================
    COUNT(*) AS TotalAssets,
    SUM(Price) AS TotalValue,
    ROUND(SUM(Price) / 10000, 2) AS TotalValueWan,
    
    -- ================== 各状态数量 ==================
    SUM(IsInUse) AS InUseAssets,
    SUM(IsIdle) AS IdleAssets,
    SUM(IsMaintenance) AS MaintenanceAssets,
    SUM(IsFaulty) AS FaultAssets,
    SUM(IsScrapped) AS ScrappedAssets,
    
    -- ================== 各状态比率 ==================
    ROUND(SUM(IsInUse) * 100.0 / COUNT(*), 2) AS InUseRate,
    ROUND(SUM(IsIdle) * 100.0 / COUNT(*), 2) AS IdleRate,
    ROUND(SUM(IsMaintenance) * 100.0 / COUNT(*), 2) AS MaintenanceRate,
    ROUND(SUM(IsFaulty) * 100.0 / COUNT(*), 2) AS FaultRate,
    ROUND(SUM(IsScrapped) * 100.0 / COUNT(*), 2) AS ScrappedRate,
    
    -- ================== 计算的KPI ==================
    -- 平均利用率 = 在用率 - (空闲率 * 0.3) - (维修率 * 0.5)
    ROUND(
        SUM(IsInUse) * 100.0 / COUNT(*) - 
        SUM(IsIdle) * 30.0 / COUNT(*) - 
        SUM(IsMaintenance) * 50.0 / COUNT(*), 
        2
    ) AS AverageUtilization,
    
    -- 健康度 = 100 - 故障率 - 报废率
    ROUND(
        100 - SUM(IsFaulty) * 100.0 / COUNT(*) - SUM(IsScrapped) * 100.0 / COUNT(*), 
        2
    ) AS HealthScore,
    
    -- ================== 统计信息 ==================
    COUNT(DISTINCT AssetTypeId) AS TypeCount,
    COUNT(DISTINCT CASE WHEN InheritedDepartmentId > 0 THEN InheritedDepartmentId END) AS DepartmentCount,
    COUNT(DISTINCT RegionId) AS RegionCount,
    
    -- ================== 时间信息 ==================
    MIN(CreatedAt) AS EarliestAsset,
    MAX(CreatedAt) AS LatestAsset,
    ROUND(AVG(AssetAgeMonths), 1) AS AverageAgeMonths,
    
    -- ================== 价值分析 ==================
    ROUND(AVG(Price), 2) AS AverageValue,
    MAX(Price) AS MaxValue,
    MIN(CASE WHEN Price > 0 THEN Price END) AS MinValue,
    
    -- ================== 趋势数据（与上月比较） ==================
    (SELECT COUNT(*) FROM assets a_sub WHERE a_sub.CreatedAt >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)) AS NewAssetsLast30Days,
    (SELECT COUNT(*) FROM assets a_sub WHERE a_sub.CreatedAt >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)) AS NewAssetsLast7Days

FROM v_assets_enhanced;

-- ============================================================================
-- 5. 价值分布增强视图 (v_asset_value_distribution_enhanced)
-- ============================================================================

DROP VIEW IF EXISTS v_asset_value_distribution_enhanced;

CREATE VIEW v_asset_value_distribution_enhanced AS
SELECT 
    vr.range_label AS ValueRange,
    vr.range_color AS RangeColor,
    vr.sort_order AS SortOrder,
    COALESCE(asset_stats.AssetCount, 0) AS AssetCount,
    COALESCE(asset_stats.TotalValue, 0) AS TotalValue,
    COALESCE(asset_stats.TotalValueWan, 0) AS TotalValueWan,
    COALESCE(asset_stats.AverageValue, 0) AS AverageValue,
    COALESCE(asset_stats.InUseCount, 0) AS InUseCount,
    COALESCE(asset_stats.FaultCount, 0) AS FaultCount,
    COALESCE(asset_stats.Percentage, 0) AS Percentage

FROM asset_value_ranges vr

LEFT JOIN (
    SELECT 
        ValueRange,
        COUNT(*) AS AssetCount,
        SUM(Price) AS TotalValue,
        ROUND(SUM(Price) / 10000, 2) AS TotalValueWan,
        ROUND(AVG(Price), 2) AS AverageValue,
        SUM(IsInUse) AS InUseCount,
        SUM(IsFaulty) AS FaultCount,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM assets), 2) AS Percentage
    FROM v_assets_enhanced
    GROUP BY ValueRange
) asset_stats ON vr.range_label = asset_stats.ValueRange

WHERE vr.is_active = TRUE
ORDER BY vr.sort_order;

-- ============================================================================
-- 6. 创建物化视图表（高性能方案）
-- ============================================================================

-- 物化视图：资产统计快照
DROP TABLE IF EXISTS mv_asset_statistics;
CREATE TABLE mv_asset_statistics (
    dimension_type VARCHAR(20) NOT NULL,
    dimension_key VARCHAR(50) NOT NULL,
    dimension_name VARCHAR(100) NOT NULL,
    total_count INT NOT NULL DEFAULT 0,
    in_use_count INT NOT NULL DEFAULT 0,
    idle_count INT NOT NULL DEFAULT 0,
    maintenance_count INT NOT NULL DEFAULT 0,
    fault_count INT NOT NULL DEFAULT 0,
    total_value DECIMAL(18,2) NOT NULL DEFAULT 0,
    in_use_rate DECIMAL(5,2) NOT NULL DEFAULT 0,
    fault_rate DECIMAL(5,2) NOT NULL DEFAULT 0,
    percentage DECIMAL(5,2) NOT NULL DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (dimension_type, dimension_key),
    INDEX idx_dimension_type (dimension_type),
    INDEX idx_total_count (total_count DESC),
    INDEX idx_last_updated (last_updated)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产统计数据物化视图';

-- 物化视图：KPI快照
DROP TABLE IF EXISTS mv_asset_kpi;
CREATE TABLE mv_asset_kpi (
    snapshot_date DATE NOT NULL PRIMARY KEY,
    total_assets INT NOT NULL,
    total_value_wan DECIMAL(12,2) NOT NULL,
    in_use_rate DECIMAL(5,2) NOT NULL,
    idle_rate DECIMAL(5,2) NOT NULL,
    maintenance_rate DECIMAL(5,2) NOT NULL,
    fault_rate DECIMAL(5,2) NOT NULL,
    average_utilization DECIMAL(5,2) NOT NULL,
    health_score DECIMAL(5,2) NOT NULL,
    type_count INT NOT NULL,
    department_count INT NOT NULL,
    region_count INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='KPI数据物化视图';

-- ============================================================================
-- 7. 物化视图刷新存储过程
-- ============================================================================

DELIMITER //

-- 刷新统计数据物化视图
DROP PROCEDURE IF EXISTS RefreshAssetStatistics //
CREATE PROCEDURE RefreshAssetStatistics()
BEGIN
    DECLARE start_time TIMESTAMP DEFAULT NOW();
    DECLARE record_count INT DEFAULT 0;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        INSERT INTO view_performance_stats (view_name, query_type, execution_time_ms, record_count) 
        VALUES ('mv_asset_statistics', 'refresh_error', TIMESTAMPDIFF(MICROSECOND, start_time, NOW()) / 1000, 0);
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 清空现有数据
    DELETE FROM mv_asset_statistics;
    
    -- 插入新数据
    INSERT INTO mv_asset_statistics (
        dimension_type, dimension_key, dimension_name, total_count, 
        in_use_count, idle_count, maintenance_count, fault_count,
        total_value, in_use_rate, fault_rate, percentage
    )
    SELECT 
        DimensionType, DimensionKey, DimensionName, TotalCount,
        InUseCount, IdleCount, MaintenanceCount, FaultCount,
        TotalValue, InUseRate, FaultRate, Percentage
    FROM v_asset_statistics_fast;
    
    GET DIAGNOSTICS record_count = ROW_COUNT;
    
    -- 更新统计信息
    ANALYZE TABLE mv_asset_statistics;
    
    COMMIT;
    
    -- 记录性能统计
    INSERT INTO view_performance_stats (view_name, query_type, execution_time_ms, record_count, query_date) 
    VALUES ('mv_asset_statistics', 'refresh', TIMESTAMPDIFF(MICROSECOND, start_time, NOW()) / 1000, record_count, CURDATE());
    
END //

-- 刷新KPI数据物化视图
DROP PROCEDURE IF EXISTS RefreshAssetKPI //
CREATE PROCEDURE RefreshAssetKPI()
BEGIN
    DECLARE start_time TIMESTAMP DEFAULT NOW();
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        INSERT INTO view_performance_stats (view_name, query_type, execution_time_ms, record_count) 
        VALUES ('mv_asset_kpi', 'refresh_error', TIMESTAMPDIFF(MICROSECOND, start_time, NOW()) / 1000, 0);
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 插入或更新今日KPI数据
    INSERT INTO mv_asset_kpi (
        snapshot_date, total_assets, total_value_wan, in_use_rate, idle_rate, 
        maintenance_rate, fault_rate, average_utilization, health_score,
        type_count, department_count, region_count
    )
    SELECT 
        CURDATE(), TotalAssets, TotalValueWan, InUseRate, IdleRate,
        MaintenanceRate, FaultRate, AverageUtilization, HealthScore,
        TypeCount, DepartmentCount, RegionCount
    FROM v_asset_kpi_enhanced
    ON DUPLICATE KEY UPDATE
        total_assets = VALUES(total_assets),
        total_value_wan = VALUES(total_value_wan),
        in_use_rate = VALUES(in_use_rate),
        idle_rate = VALUES(idle_rate),
        maintenance_rate = VALUES(maintenance_rate),
        fault_rate = VALUES(fault_rate),
        average_utilization = VALUES(average_utilization),
        health_score = VALUES(health_score),
        type_count = VALUES(type_count),
        department_count = VALUES(department_count),
        region_count = VALUES(region_count);
    
    COMMIT;
    
    -- 记录性能统计
    INSERT INTO view_performance_stats (view_name, query_type, execution_time_ms, record_count, query_date) 
    VALUES ('mv_asset_kpi', 'refresh', TIMESTAMPDIFF(MICROSECOND, start_time, NOW()) / 1000, 1, CURDATE());
    
END //

-- 完整刷新所有物化视图
DROP PROCEDURE IF EXISTS RefreshAllAssetViews //
CREATE PROCEDURE RefreshAllAssetViews()
BEGIN
    CALL RefreshAssetStatistics();
    CALL RefreshAssetKPI();
    
    -- 清理过期性能统计（保留30天）
    DELETE FROM view_performance_stats 
    WHERE query_date < DATE_SUB(CURDATE(), INTERVAL 30 DAY);
END //

DELIMITER ;

-- ============================================================================
-- 8. 创建定时任务（每小时刷新）
-- ============================================================================

-- 启用事件调度器
SET GLOBAL event_scheduler = ON;

-- 创建定时刷新任务
DROP EVENT IF EXISTS refresh_asset_views_hourly;
CREATE EVENT refresh_asset_views_hourly
ON SCHEDULE EVERY 1 HOUR
STARTS CURRENT_TIMESTAMP
DO CALL RefreshAllAssetViews();

-- ============================================================================
-- 9. 创建性能优化索引
-- ============================================================================

-- 为assets表创建复合索引
DROP INDEX IF EXISTS idx_assets_performance ON assets;
CREATE INDEX idx_assets_performance ON assets(Status, AssetTypeId, LocationId, CreatedAt);

DROP INDEX IF EXISTS idx_assets_price_status ON assets;
CREATE INDEX idx_assets_price_status ON assets(Price, Status) WHERE Price IS NOT NULL;

-- 为locations表创建索引
DROP INDEX IF EXISTS idx_locations_path_dept ON locations;
CREATE INDEX idx_locations_path_dept ON locations(Path, DefaultDepartmentId);

DROP INDEX IF EXISTS idx_locations_type_active ON locations;
CREATE INDEX idx_locations_type_active ON locations(Type, IsActive);

-- 为departments表创建索引
DROP INDEX IF EXISTS idx_departments_active ON departments;
CREATE INDEX idx_departments_active ON departments(IsActive, ManagerId);

-- ============================================================================
-- 10. 初始化数据和验证
-- ============================================================================

-- 初始刷新物化视图
CALL RefreshAllAssetViews();

-- 验证视图创建结果
SELECT 
    TABLE_NAME as ViewName,
    TABLE_TYPE as Type,
    TABLE_COMMENT as Comment
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
    AND (TABLE_TYPE = 'VIEW' OR TABLE_NAME LIKE 'mv_asset%')
    AND TABLE_NAME LIKE '%asset%'
ORDER BY TABLE_TYPE DESC, TABLE_NAME;

-- 显示统计摘要
SELECT 
    '视图数据验证' AS Category,
    (SELECT COUNT(*) FROM v_assets_enhanced) AS EnhancedViewRecords,
    (SELECT COUNT(*) FROM v_asset_statistics_fast) AS StatisticsRecords,
    (SELECT COUNT(*) FROM mv_asset_statistics) AS MaterializedRecords,
    (SELECT COUNT(*) FROM v_asset_matrix_enhanced) AS MatrixRecords;

-- 显示KPI示例
SELECT '当前KPI数据' AS Title;
SELECT * FROM v_asset_kpi_enhanced;

-- 显示价值分布
SELECT '价值分布数据' AS Title;
SELECT * FROM v_asset_value_distribution_enhanced;

-- 显示性能统计
SELECT '性能统计' AS Title;
SELECT view_name, query_type, AVG(execution_time_ms) as avg_time_ms, COUNT(*) as execution_count
FROM view_performance_stats 
WHERE query_date = CURDATE()
GROUP BY view_name, query_type
ORDER BY avg_time_ms DESC;