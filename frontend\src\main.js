/**
 * IT资产管理系统 - 主入口文件
 * 文件路径: src/main.js
 * 功能描述: 初始化Vue应用，注册全局组件和插件
 * 
 * [已修复]: 重新添加了被误删的 useNotificationStore 的导入。
 */

import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'

// Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
// 导入所有 Element Plus 图标
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 全局样式
import './styles/global.scss'

// [已修复] 重新添加了 useNotificationStore 的导入
import { useNotificationStore } from './stores/modules/notification'

// 创建应用实例
const app = createApp(App)
const pinia = createPinia()

// 注册所有 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用插件
app.use(router)
app.use(pinia)
app.use(ElementPlus, {
  locale: zhCn,
  size: 'default'
})

// 通知轮询将在用户登录后由 DefaultLayout 或登录组件启动
// 移除全局自动轮询，避免未登录用户的无效API调用

// 挂载应用
app.mount('#app')

// 开发环境下的工具
if (import.meta.env.DEV) {
  // 全局store对象供开发调试使用
  window.__pinia = pinia
}

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('Vue全局错误:', err, info)

  // 特殊处理 ECharts 相关错误
  if (err.message && err.message.includes('Cannot read properties of undefined')) {
    console.warn('检测到可能的 ECharts 错误，建议检查图表数据结构')
  }
}

// 全局未捕获的 Promise 错误
window.addEventListener('unhandledrejection', event => {
  console.error('未捕获的 Promise 错误:', event.reason)
  event.preventDefault() // 阻止默认的错误处理
})

// 全局 JavaScript 错误
window.addEventListener('error', event => {
  console.error('全局 JavaScript 错误:', event.error)
})

// 优化事件监听器性能，避免 passive event listener 警告
if (typeof window !== 'undefined') {
  // 为常见的滚动事件添加 passive 选项
  const originalAddEventListener = EventTarget.prototype.addEventListener
  const passiveEvents = ['scroll', 'wheel', 'touchstart', 'touchmove', 'touchend', 'touchcancel', 'mousewheel', 'DOMMouseScroll']

  EventTarget.prototype.addEventListener = function(type, listener, options) {
    // 如果是被动事件且没有指定选项，设置为 passive
    if (passiveEvents.includes(type)) {
      if (typeof options !== 'object') {
        options = { passive: true }
      } else if (options && options.passive === undefined) {
        options = { ...options, passive: true }
      }
    }
    return originalAddEventListener.call(this, type, listener, options)
  }

  // 同时优化 removeEventListener
  const originalRemoveEventListener = EventTarget.prototype.removeEventListener
  EventTarget.prototype.removeEventListener = function(type, listener, options) {
    if (passiveEvents.includes(type) && typeof options !== 'object') {
      options = { passive: true }
    } else if (passiveEvents.includes(type) && typeof options === 'object' && options.passive === undefined) {
      options = { ...options, passive: true }
    }
    return originalRemoveEventListener.call(this, type, listener, options)
  }
}