using System;
using System.Collections.Generic;

namespace ItAssetsSystem.Application.Features.AssetStatistics.Dtos
{
    /// <summary>
    /// 资产分析工作台完整数据DTO
    /// 用于首次加载时获取所有必要的数据
    /// </summary>
    public class AssetAnalyticsWorkbenchDto
    {
        /// <summary>
        /// KPI指标数据
        /// </summary>
        public AssetKpiDataDto KpiData { get; set; } = new();

        /// <summary>
        /// 按资产类型统计
        /// </summary>
        public List<AssetTypeStatisticsDto> TypeStatistics { get; set; } = new();

        /// <summary>
        /// 按区域统计
        /// </summary>
        public List<AssetRegionStatisticsDto> RegionStatistics { get; set; } = new();

        /// <summary>
        /// 按部门统计
        /// </summary>
        public List<AssetDepartmentStatisticsDto> DepartmentStatistics { get; set; } = new();

        /// <summary>
        /// 时间序列数据（设备使用时段分析）
        /// </summary>
        public AssetTimeSeriesDataDto TimeSeriesData { get; set; } = new();

        /// <summary>
        /// 资产价值分布数据
        /// </summary>
        public AssetValueDistributionDto ValueDistribution { get; set; } = new();

        /// <summary>
        /// 部门资产矩阵数据
        /// </summary>
        public AssetMatrixDataDto MatrixData { get; set; } = new();

        /// <summary>
        /// 资产状态分布数据
        /// </summary>
        public AssetStatusDistributionDto StatusDistribution { get; set; } = new();

        /// <summary>
        /// 筛选选项数据
        /// </summary>
        public AssetFilterOptionsDto FilterOptions { get; set; } = new();

        /// <summary>
        /// 数据更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// KPI指标数据
    /// </summary>
    public class AssetKpiDataDto
    {
        /// <summary>
        /// 资产总数
        /// </summary>
        public int TotalAssets { get; set; }

        /// <summary>
        /// 资产总价值（万元）
        /// </summary>
        public decimal TotalValue { get; set; }

        /// <summary>
        /// 在线率
        /// </summary>
        public double OnlineRate { get; set; }

        /// <summary>
        /// 故障中数量
        /// </summary>
        public int FaultCount { get; set; }

        /// <summary>
        /// 总数趋势（相比上期）
        /// </summary>
        public double TotalTrend { get; set; }

        /// <summary>
        /// 价值趋势（相比上期）
        /// </summary>
        public double ValueTrend { get; set; }

        /// <summary>
        /// 在线率趋势（相比上期）
        /// </summary>
        public double OnlineRateTrend { get; set; }

        /// <summary>
        /// 故障趋势（相比上期）
        /// </summary>
        public double FaultTrend { get; set; }
    }

    /// <summary>
    /// 时间序列数据
    /// </summary>
    public class AssetTimeSeriesDataDto
    {
        /// <summary>
        /// 时间标签
        /// </summary>
        public List<string> TimeLabels { get; set; } = new();

        /// <summary>
        /// 在线设备数据
        /// </summary>
        public List<int> OnlineData { get; set; } = new();

        /// <summary>
        /// 离线设备数据
        /// </summary>
        public List<int> OfflineData { get; set; } = new();

        /// <summary>
        /// 维护中设备数据
        /// </summary>
        public List<int> MaintenanceData { get; set; } = new();
    }

    /// <summary>
    /// 资产价值分布数据
    /// </summary>
    public class AssetValueDistributionDto
    {
        /// <summary>
        /// 价值区间标签
        /// </summary>
        public List<string> ValueRanges { get; set; } = new();

        /// <summary>
        /// 各区间资产数量
        /// </summary>
        public List<int> AssetCounts { get; set; } = new();

        /// <summary>
        /// 各区间总价值
        /// </summary>
        public List<decimal> TotalValues { get; set; } = new();
    }

    /// <summary>
    /// 部门资产矩阵数据
    /// </summary>
    public class AssetMatrixDataDto
    {
        /// <summary>
        /// 部门列表
        /// </summary>
        public List<string> Departments { get; set; } = new();

        /// <summary>
        /// 资产类型列表
        /// </summary>
        public List<string> AssetTypes { get; set; } = new();

        /// <summary>
        /// 矩阵数据（部门x资产类型的数量）
        /// </summary>
        public List<List<int>> MatrixData { get; set; } = new();
    }

    /// <summary>
    /// 资产状态分布数据
    /// </summary>
    public class AssetStatusDistributionDto
    {
        /// <summary>
        /// 状态标签
        /// </summary>
        public List<string> StatusLabels { get; set; } = new();

        /// <summary>
        /// 各状态数量
        /// </summary>
        public List<int> StatusCounts { get; set; } = new();

        /// <summary>
        /// 各状态百分比
        /// </summary>
        public List<double> StatusPercentages { get; set; } = new();
    }

    /// <summary>
    /// 筛选选项数据
    /// </summary>
    public class AssetFilterOptionsDto
    {
        /// <summary>
        /// 资产类型选项
        /// </summary>
        public List<AssetTypeDto> AssetTypes { get; set; } = new();

        /// <summary>
        /// 区域选项
        /// </summary>
        public List<AssetRegionDto> Regions { get; set; } = new();

        /// <summary>
        /// 部门选项
        /// </summary>
        public List<AssetDepartmentDto> Departments { get; set; } = new();

        /// <summary>
        /// 状态选项
        /// </summary>
        public List<AssetStatusOptionDto> Statuses { get; set; } = new();
    }

    /// <summary>
    /// 资产状态选项
    /// </summary>
    public class AssetStatusOptionDto
    {
        /// <summary>
        /// 状态值
        /// </summary>
        public int Value { get; set; }

        /// <summary>
        /// 状态名称
        /// </summary>
        public string Label { get; set; } = string.Empty;

        /// <summary>
        /// 状态颜色
        /// </summary>
        public string Color { get; set; } = string.Empty;
    }
}
