-- 专门检查 v_asset_kpi_enhanced 视图的诊断脚本

-- ============================================================================
-- 1. 检查视图是否存在
-- ============================================================================
SELECT 'KPI View Existence' AS CheckType;

SELECT 
    TABLE_NAME,
    TABLE_TYPE,
    ENGINE,
    TABLE_COMMENT
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'v_asset_kpi_enhanced';

-- ============================================================================
-- 2. 检查视图定义
-- ============================================================================
SELECT 'KPI View Definition' AS CheckType;

SHOW CREATE VIEW v_asset_kpi_enhanced;

-- ============================================================================
-- 3. 检查基础依赖表
-- ============================================================================
SELECT 'Base Tables Check' AS CheckType;

-- 检查assets表
SELECT 'assets表数据' as TableName, COUNT(*) as RecordCount FROM assets;

-- 检查v_assets_enhanced视图
SELECT 'v_assets_enhanced视图数据' as ViewName, COUNT(*) as RecordCount FROM v_assets_enhanced;

-- ============================================================================
-- 4. 测试KPI视图查询
-- ============================================================================
SELECT 'KPI View Query Test' AS CheckType;

-- 直接查询KPI视图
SELECT * FROM v_asset_kpi_enhanced;

-- ============================================================================
-- 5. 分步测试KPI计算
-- ============================================================================
SELECT 'Step by Step KPI Calculation' AS CheckType;

-- 测试基础统计
SELECT 
    'Basic Count' as Metric,
    COUNT(*) AS Value
FROM v_assets_enhanced

UNION ALL

SELECT 
    'Total Assets with Price' as Metric,
    COUNT(*) AS Value
FROM v_assets_enhanced
WHERE Price > 0

UNION ALL

SELECT 
    'InUse Assets' as Metric,
    SUM(IsInUse) AS Value
FROM v_assets_enhanced

UNION ALL

SELECT 
    'Idle Assets' as Metric,
    SUM(IsIdle) AS Value
FROM v_assets_enhanced

UNION ALL

SELECT 
    'Fault Assets' as Metric,
    SUM(IsFaulty) AS Value
FROM v_assets_enhanced;

-- ============================================================================
-- 6. 检查具体的KPI计算
-- ============================================================================
SELECT 'Manual KPI Calculation' AS CheckType;

SELECT 
    COUNT(*) AS TotalAssets,
    SUM(Price) AS TotalValue,
    ROUND(SUM(Price) / 10000, 2) AS TotalValueWan,
    SUM(IsInUse) AS InUseAssets,
    SUM(IsIdle) AS IdleAssets,
    SUM(IsMaintenance) AS MaintenanceAssets,
    SUM(IsFaulty) AS FaultAssets,
    SUM(IsScrapped) AS ScrappedAssets,
    ROUND(SUM(IsInUse) * 100.0 / COUNT(*), 2) AS InUseRate,
    ROUND(SUM(IsIdle) * 100.0 / COUNT(*), 2) AS IdleRate,
    ROUND(SUM(IsMaintenance) * 100.0 / COUNT(*), 2) AS MaintenanceRate,
    ROUND(SUM(IsFaulty) * 100.0 / COUNT(*), 2) AS FaultRate,
    ROUND(SUM(IsScrapped) * 100.0 / COUNT(*), 2) AS ScrappedRate
FROM v_assets_enhanced;

-- ============================================================================
-- 7. 检查可能的错误原因
-- ============================================================================
SELECT 'Error Analysis' AS CheckType;

-- 检查v_assets_enhanced中的字段是否存在问题
SELECT 
    'NULL AssetAgeMonths Count' as Issue,
    COUNT(*) as Count
FROM v_assets_enhanced 
WHERE AssetAgeMonths IS NULL

UNION ALL

SELECT 
    'NULL Price Count' as Issue,
    COUNT(*) as Count
FROM v_assets_enhanced 
WHERE Price IS NULL

UNION ALL

SELECT 
    'Invalid Status Count' as Issue,
    COUNT(*) as Count
FROM v_assets_enhanced 
WHERE Status NOT IN (0, 1, 2, 3, 4);

-- ============================================================================
-- 8. 检查最近资产创建情况
-- ============================================================================
SELECT 'Recent Assets Analysis' AS CheckType;

SELECT 
    'Last 30 Days' as Period,
    COUNT(*) as NewAssets
FROM assets 
WHERE CreatedAt >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)

UNION ALL

SELECT 
    'Last 7 Days' as Period,
    COUNT(*) as NewAssets
FROM assets 
WHERE CreatedAt >= DATE_SUB(CURDATE(), INTERVAL 7 DAY);

SELECT 'KPI View Diagnostic Complete' AS Result;