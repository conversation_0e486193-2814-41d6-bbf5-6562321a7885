import { defineStore } from 'pinia'
import { notificationApi } from '@/api/notification'
import { useUserStore } from './user'

export const useNotificationStore = defineStore('notification', {
  state: () => ({
    notifications: [],
    unreadCount: 0,
    loading: false,
    lastFetchTime: null,
    pollingInterval: null,
    total: 0
  }),

  getters: {
    unreadNotifications: (state) => {
      return state.notifications.filter(n => !n.isRead)
    },

    taskNotifications: (state) => {
      // 修复任务通知过滤逻辑 - 包含更多任务相关类型
      const taskTypes = [
        'task', 'comment', 'mention',
        'TaskAssigned', 'TaskStatusChanged', 'TaskContentChanged',
        'TaskComment', 'TaskAttachmentAdded', 'TaskMention', 'TaskOverdue',
        'Test' // 包含测试通知
      ]
      return state.notifications.filter(n => taskTypes.includes(n.type))
    },

    systemNotifications: (state) => {
      return state.notifications.filter(n => n.type === 'system')
    },

    hasUnread: (state) => {
      return state.unreadCount > 0
    }
  },

  actions: {
    async fetchNotifications(options = {}) {
      // 支持强制刷新或传递分页参数
      const isForce = typeof options === 'boolean' ? options : options.force || false
      const params = typeof options === 'object' ? options : {}
      
      // 避免频繁请求，5分钟内不重复请求
      const now = Date.now()
      if (!isForce && this.lastFetchTime && (now - this.lastFetchTime) < 5 * 60 * 1000) {
        return
      }

      try {
        this.loading = true
        console.log('开始获取通知，参数:', params)
        const response = await notificationApi.getNotifications(params)
        console.log('API响应:', response)
        
        if (response.success) {
          console.log('原始通知数据:', response.data.notifications)

          this.notifications = response.data.notifications.map(n => ({
            id: n.id,
            notificationId: n.id,
            type: n.type,
            title: n.title,
            message: n.content,
            content: n.content,
            timestamp: n.createdAt || n.creationTimestamp || n.timestamp,
            createdAt: n.createdAt || n.creationTimestamp,
            creationTimestamp: n.creationTimestamp || n.createdAt,
            taskId: (n.resourceType === 'Task' || n.referenceType === 'Task') ? (n.resourceId || n.referenceId) : null,
            resourceType: n.resourceType || n.referenceType,
            resourceId: n.resourceId || n.referenceId,
            referenceType: n.referenceType || n.resourceType,
            referenceId: n.referenceId || n.resourceId,
            read: n.isRead,
            isRead: n.isRead
          }))

          console.log('处理后的通知数据:', this.notifications)
          console.log('通知数量:', this.notifications.length)

          this.unreadCount = response.data.pagination.unreadCount
          this.total = response.data.pagination.total || this.notifications.length
          this.lastFetchTime = now
        } else {
          console.error('API响应失败:', response.message)
          throw new Error(response.message || '获取通知失败')
        }
      } catch (error) {
        console.error('获取通知失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    async fetchUnreadCount() {
      try {
        // 检查用户是否已登录
        const userStore = useUserStore()
        if (!userStore.isLogin || !userStore.userInfo?.id) {
          console.log('用户未登录，跳过获取未读通知数量')
          this.unreadCount = 0
          return
        }

        const response = await notificationApi.getUnreadCount()

        if (response.success) {
          this.unreadCount = response.data
        } else {
          // 如果是未登录错误，不抛出异常，只是重置数量
          if (response.message?.includes('未登录')) {
            console.log('用户未登录，重置未读数量为0')
            this.unreadCount = 0
            return
          }
          throw new Error(response.message || '获取未读数量失败')
        }
      } catch (error) {
        console.error('获取未读数量失败:', error)
        // 如果是网络错误或认证错误，重置未读数量
        if (error.message?.includes('401') || error.message?.includes('未登录')) {
          this.unreadCount = 0
        }
        throw error
      }
    },

    async markAsRead(id) {
      try {
        const response = await notificationApi.markAsRead(id)
        
        if (response.success) {
          const notification = this.notifications.find(n => n.id === id)
          if (notification && !notification.isRead) {
            notification.read = true
            notification.isRead = true
            this.unreadCount = Math.max(0, this.unreadCount - 1)
          }
        } else {
          throw new Error(response.message || '标记失败')
        }
      } catch (error) {
        console.error('标记通知为已读失败:', error)
        throw error
      }
    },

    async markAllAsRead() {
      try {
        const response = await notificationApi.markAllAsRead()
        
        if (response.success) {
          this.notifications.forEach(notification => {
            notification.read = true
            notification.isRead = true
          })
          this.unreadCount = 0
        } else {
          throw new Error(response.message || '标记失败')
        }
      } catch (error) {
        console.error('标记所有通知为已读失败:', error)
        throw error
      }
    },

    async deleteNotification(id) {
      try {
        const response = await notificationApi.deleteNotification(id)
        
        if (response.success) {
          const index = this.notifications.findIndex(n => n.id === id)
          if (index > -1) {
            const notification = this.notifications[index]
            if (!notification.read) {
              this.unreadCount = Math.max(0, this.unreadCount - 1)
            }
            this.notifications.splice(index, 1)
          }
        } else {
          throw new Error(response.message || '删除失败')
        }
      } catch (error) {
        console.error('删除通知失败:', error)
        throw error
      }
    },

    // 添加新通知（通过WebSocket或轮询时使用）
    addNotification(notification) {
      // 检查是否已存在相同ID的通知
      const existingIndex = this.notifications.findIndex(n => n.id === notification.id || 
        (notification.notificationId && n.id === notification.notificationId))
      
      if (existingIndex !== -1) {
        // 更新已存在的通知
        this.notifications[existingIndex] = {
          ...this.notifications[existingIndex],
          id: notification.id || notification.notificationId,
          type: notification.type,
          title: notification.title,
          message: notification.content || notification.message,
          timestamp: notification.createdAt || notification.creationTimestamp || notification.timestamp,
          taskId: notification.resourceType === 'Task' ? notification.resourceId : notification.taskId,
          read: notification.isRead || notification.read || false
        }
      } else {
        // 添加新通知并更新未读数量
        this.notifications.unshift({
          id: notification.id || notification.notificationId,
          type: notification.type,
          title: notification.title,
          message: notification.content || notification.message,
          timestamp: notification.createdAt || notification.creationTimestamp || notification.timestamp,
          taskId: notification.resourceType === 'Task' ? notification.resourceId : notification.taskId,
          read: notification.isRead || notification.read || false
        })
        
        if (!(notification.isRead || notification.read)) {
          this.unreadCount++
        }
      }
      
      // 按时间排序
      this.notifications.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
    },

    // 启动定时刷新未读数量
    startPolling() {
      // 检查用户是否已登录
      const userStore = useUserStore()
      if (!userStore.isLogin || !userStore.userInfo?.id) {
        console.log('用户未登录，不启动通知轮询')
        return
      }

      if (this.pollingInterval) {
        console.log('通知轮询已在运行，避免重复启动')
        return // 避免重复启动
      }

      console.log('启动通知轮询，每30秒检查一次未读数量')
      // 每30秒检查一次未读数量
      this.pollingInterval = setInterval(() => {
        this.fetchUnreadCount().catch((error) => {
          // 如果是认证错误，停止轮询
          if (error.message?.includes('401') || error.message?.includes('未登录')) {
            console.log('检测到认证错误，停止通知轮询')
            this.stopPolling()
          }
        })
      }, 30000)
    },

    // 停止轮询
    stopPolling() {
      if (this.pollingInterval) {
        console.log('停止通知轮询')
        clearInterval(this.pollingInterval)
        this.pollingInterval = null
      }
    },

    // 重置通知状态（用户登出时调用）
    resetNotificationState() {
      console.log('重置通知状态')
      this.stopPolling()
      this.notifications = []
      this.unreadCount = 0
      this.total = 0
      this.lastFetchTime = null
    }
  }
})