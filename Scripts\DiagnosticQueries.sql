-- 快速诊断查询 - 确定视图数据状态

-- ============================================================================
-- 1. 检查视图数据量
-- ============================================================================

SELECT 'Data Count Check' AS DiagnosticType;

SELECT 'v_assets_enhanced' as ViewName, COUNT(*) as RecordCount FROM v_assets_enhanced
UNION ALL
SELECT 'v_asset_kpi_enhanced' as ViewName, COUNT(*) as RecordCount FROM v_asset_kpi_enhanced
UNION ALL  
SELECT 'v_asset_statistics_fast' as ViewName, COUNT(*) as RecordCount FROM v_asset_statistics_fast
UNION ALL
SELECT 'assets (base table)' as ViewName, COUNT(*) as RecordCount FROM assets;

-- ============================================================================
-- 2. 检查KPI视图具体数据
-- ============================================================================

SELECT 'KPI Data Check' AS DiagnosticType;

SELECT 
    TotalAssets,
    TotalValueWan,
    InUseRate,
    FaultRate,
    IdleRate,
    MaintenanceRate,
    AverageUtilization,
    HealthScore,
    FaultAssets
FROM v_asset_kpi_enhanced;

-- ============================================================================
-- 3. 检查统计视图具体数据
-- ============================================================================

SELECT 'Statistics Data Check' AS DiagnosticType;

SELECT 
    DimensionType,
    COUNT(*) as RecordCount,
    SUM(TotalCount) as TotalAssets
FROM v_asset_statistics_fast
GROUP BY DimensionType;

-- ============================================================================
-- 4. 检查基础表数据
-- ============================================================================

SELECT 'Base Tables Status' AS DiagnosticType;

SELECT 
    'assets' as TableName,
    COUNT(*) as RecordCount,
    COUNT(DISTINCT AssetTypeId) as UniqueTypes,
    COUNT(DISTINCT LocationId) as UniqueLocations,
    COUNT(DISTINCT Status) as UniqueStatuses
FROM assets

UNION ALL

SELECT 
    'assettypes' as TableName,
    COUNT(*) as RecordCount,
    0 as UniqueTypes,
    0 as UniqueLocations, 
    0 as UniqueStatuses
FROM assettypes

UNION ALL

SELECT 
    'departments' as TableName,
    COUNT(*) as RecordCount,
    0 as UniqueTypes,
    0 as UniqueLocations,
    0 as UniqueStatuses  
FROM departments

UNION ALL

SELECT 
    'locations' as TableName,
    COUNT(*) as RecordCount,
    0 as UniqueTypes,
    0 as UniqueLocations,
    0 as UniqueStatuses
FROM locations;

SELECT 'Diagnostic Complete - Please share these results' AS Message;