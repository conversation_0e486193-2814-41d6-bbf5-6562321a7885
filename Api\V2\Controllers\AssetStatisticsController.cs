using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.AssetStatistics.Dtos;
using ItAssetsSystem.Application.Features.AssetStatistics.Services;
using ItAssetsSystem.Core.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Api.V2.Controllers
{
    /// <summary>
    /// 资产统计分析API控制器 (V2)
    /// </summary>
    [ApiController]
    [Route("api/v2/asset-statistics")]
    [Authorize]
    public class AssetStatisticsController : ControllerBase
    {
        private readonly IAssetStatisticsService _assetStatisticsService;
        private readonly ILogger<AssetStatisticsController> _logger;

        public AssetStatisticsController(
            IAssetStatisticsService assetStatisticsService,
            ILogger<AssetStatisticsController> logger)
        {
            _assetStatisticsService = assetStatisticsService ?? throw new ArgumentNullException(nameof(assetStatisticsService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 获取资产总体统计
        /// </summary>
        /// <param name="parameters">查询参数</param>
        /// <returns>总体统计数据</returns>
        [HttpGet("overall")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<AssetOverallStatisticsDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<AssetOverallStatisticsDto>))]
        public async Task<IActionResult> GetOverallStatistics([FromQuery] AssetStatisticsQueryDto parameters)
        {
            try
            {
                var result = await _assetStatisticsService.GetOverallStatisticsAsync(parameters);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取资产总体统计时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<AssetOverallStatisticsDto>("获取资产总体统计时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取按资产类型统计
        /// </summary>
        /// <param name="parameters">查询参数</param>
        /// <returns>按类型统计数据</returns>
        [HttpGet("by-type")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<AssetTypeStatisticsDto>>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<List<AssetTypeStatisticsDto>>))]
        public async Task<IActionResult> GetStatisticsByType([FromQuery] AssetStatisticsQueryDto parameters)
        {
            try
            {
                var result = await _assetStatisticsService.GetStatisticsByTypeAsync(parameters);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取按资产类型统计时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<List<AssetTypeStatisticsDto>>("获取按资产类型统计时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取按区域统计
        /// </summary>
        /// <param name="parameters">查询参数</param>
        /// <returns>按区域统计数据</returns>
        [HttpGet("by-region")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<AssetRegionStatisticsDto>>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<List<AssetRegionStatisticsDto>>))]
        public async Task<IActionResult> GetStatisticsByRegion([FromQuery] AssetStatisticsQueryDto parameters)
        {
            try
            {
                var result = await _assetStatisticsService.GetStatisticsByRegionAsync(parameters);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取按区域统计时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<List<AssetRegionStatisticsDto>>("获取按区域统计时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取按部门统计
        /// </summary>
        /// <param name="parameters">查询参数</param>
        /// <returns>按部门统计数据</returns>
        [HttpGet("by-department")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<AssetDepartmentStatisticsDto>>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<List<AssetDepartmentStatisticsDto>>))]
        public async Task<IActionResult> GetStatisticsByDepartment([FromQuery] AssetStatisticsQueryDto parameters)
        {
            try
            {
                var result = await _assetStatisticsService.GetStatisticsByDepartmentAsync(parameters);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取按部门统计时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<List<AssetDepartmentStatisticsDto>>("获取按部门统计时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取周趋势数据
        /// </summary>
        /// <param name="parameters">查询参数</param>
        /// <returns>周趋势数据</returns>
        [HttpGet("trend/weekly")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<AssetTrendDataDto>>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<List<AssetTrendDataDto>>))]
        public async Task<IActionResult> GetWeeklyTrend([FromQuery] AssetTrendQueryDto parameters)
        {
            try
            {
                var result = await _assetStatisticsService.GetWeeklyTrendAsync(parameters);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取周趋势数据时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<List<AssetTrendDataDto>>("获取周趋势数据时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取日趋势数据
        /// </summary>
        /// <param name="parameters">查询参数</param>
        /// <returns>日趋势数据</returns>
        [HttpGet("trend/daily")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<AssetTrendDataDto>>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<List<AssetTrendDataDto>>))]
        public async Task<IActionResult> GetDailyTrend([FromQuery] AssetTrendQueryDto parameters)
        {
            try
            {
                var result = await _assetStatisticsService.GetDailyTrendAsync(parameters);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取日趋势数据时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<List<AssetTrendDataDto>>("获取日趋势数据时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取月趋势数据
        /// </summary>
        /// <param name="parameters">查询参数</param>
        /// <returns>月趋势数据</returns>
        [HttpGet("trend/monthly")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<AssetTrendDataDto>>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<List<AssetTrendDataDto>>))]
        public async Task<IActionResult> GetMonthlyTrend([FromQuery] AssetTrendQueryDto parameters)
        {
            try
            {
                var result = await _assetStatisticsService.GetMonthlyTrendAsync(parameters);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取月趋势数据时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<List<AssetTrendDataDto>>("获取月趋势数据时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取组合统计数据
        /// </summary>
        /// <param name="parameters">查询参数</param>
        /// <returns>组合统计数据</returns>
        [HttpGet("combined")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<AssetCombinedStatisticsDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<AssetCombinedStatisticsDto>))]
        public async Task<IActionResult> GetCombinedStatistics([FromQuery] AssetStatisticsQueryDto parameters)
        {
            try
            {
                var result = await _assetStatisticsService.GetCombinedStatisticsAsync(parameters);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取组合统计数据时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<AssetCombinedStatisticsDto>("获取组合统计数据时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取资产类型列表
        /// </summary>
        /// <returns>资产类型列表</returns>
        [HttpGet("asset-types")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<AssetTypeDto>>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<List<AssetTypeDto>>))]
        public async Task<IActionResult> GetAssetTypes()
        {
            try
            {
                var result = await _assetStatisticsService.GetAssetTypesAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取资产类型列表时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<List<AssetTypeDto>>("获取资产类型列表时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取区域列表
        /// </summary>
        /// <returns>区域列表</returns>
        [HttpGet("regions")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<AssetRegionDto>>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<List<AssetRegionDto>>))]
        public async Task<IActionResult> GetRegions()
        {
            try
            {
                var result = await _assetStatisticsService.GetRegionsAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取区域列表时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<List<AssetRegionDto>>("获取区域列表时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取部门列表
        /// </summary>
        /// <returns>部门列表</returns>
        [HttpGet("departments")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<AssetDepartmentDto>>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<List<AssetDepartmentDto>>))]
        public async Task<IActionResult> GetDepartments()
        {
            try
            {
                var result = await _assetStatisticsService.GetDepartmentsAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取部门列表时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<List<AssetDepartmentDto>>("获取部门列表时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取资产分析工作台完整数据 - 核心API
        /// </summary>
        /// <returns>工作台完整数据</returns>
        [HttpGet("analytics-workbench")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<AssetAnalyticsWorkbenchDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<AssetAnalyticsWorkbenchDto>))]
        public async Task<IActionResult> GetAnalyticsWorkbenchData()
        {
            try
            {
                _logger.LogInformation("开始获取资产分析工作台完整数据");
                
                // 如果服务是ViewBasedAssetStatisticsService，调用专用方法
                if (_assetStatisticsService is ViewBasedAssetStatisticsService viewBasedService)
                {
                    var result = await viewBasedService.GetAnalyticsWorkbenchDataAsync();
                    return Ok(result);
                }
                
                // 降级处理：使用现有方法组装数据
                var kpiData = await _assetStatisticsService.GetOverallStatisticsAsync(new AssetStatisticsQueryDto());
                var typeStats = await _assetStatisticsService.GetStatisticsByTypeAsync(new AssetStatisticsQueryDto());
                var regionStats = await _assetStatisticsService.GetStatisticsByRegionAsync(new AssetStatisticsQueryDto());
                var deptStats = await _assetStatisticsService.GetStatisticsByDepartmentAsync(new AssetStatisticsQueryDto());
                
                var workbenchData = new AssetAnalyticsWorkbenchDto
                {
                    KpiData = new AssetKpiDataDto
                    {
                        TotalAssets = kpiData.Data?.TotalAssets ?? 0,
                        TotalValue = Math.Round((kpiData.Data?.TotalAssets ?? 0) * 0.1m, 2), // 临时计算
                        OnlineRate = (double)(kpiData.Data?.NormalRate ?? 0),
                        FaultCount = kpiData.Data?.FaultAssets ?? 0,
                        IdleRate = 100 - (double)(kpiData.Data?.NormalRate ?? 0),
                        MaintenanceRate = 5.0, // 临时值
                        AverageUtilization = Math.Max(0, (double)(kpiData.Data?.NormalRate ?? 0) - 10),
                        HealthScore = Math.Max(0, 100 - (double)(kpiData.Data?.FaultRate ?? 0))
                    },
                    TypeStatistics = typeStats.Data ?? new List<AssetTypeStatisticsDto>(),
                    RegionStatistics = regionStats.Data ?? new List<AssetRegionStatisticsDto>(),
                    DepartmentStatistics = deptStats.Data ?? new List<AssetDepartmentStatisticsDto>(),
                    TimeSeriesData = new AssetTimeSeriesDataDto
                    {
                        TimeLabels = new List<string> { "00:00", "04:00", "08:00", "12:00", "16:00", "20:00" },
                        OnlineData = new List<int> { 0, 0, 0, 0, 0, 0 }, // 注释掉的功能
                        OfflineData = new List<int> { 0, 0, 0, 0, 0, 0 },
                        MaintenanceData = new List<int> { 0, 0, 0, 0, 0, 0 }
                    },
                    ValueDistribution = new AssetValueDistributionDto
                    {
                        ValueRanges = new List<string> { "0-1万", "1-5万", "5-10万", "10-50万", "50万以上" },
                        AssetCounts = new List<int> { 0, 0, 0, 0, 0 },
                        TotalValues = new List<decimal> { 0, 0, 0, 0, 0 }
                    },
                    MatrixData = new AssetMatrixDataDto
                    {
                        Departments = new List<string>(),
                        AssetTypes = new List<string>(),
                        MatrixData = new List<List<int>>()
                    },
                    StatusDistribution = new AssetStatusDistributionDto
                    {
                        StatusLabels = new List<string> { "在用", "闲置", "维修中", "故障", "报废" },
                        StatusCounts = new List<int> { 0, 0, 0, 0, 0 },
                        StatusPercentages = new List<double> { 0, 0, 0, 0, 0 }
                    },
                    FilterOptions = new AssetFilterOptionsDto
                    {
                        AssetTypes = new List<AssetTypeDto>(),
                        Regions = new List<AssetRegionDto>(),
                        Departments = new List<AssetDepartmentDto>(),
                        Statuses = new List<AssetStatusOptionDto>
                        {
                            new() { Value = 1, Label = "在用", Color = "#10b981" },
                            new() { Value = 0, Label = "闲置", Color = "#f59e0b" },
                            new() { Value = 2, Label = "维修中", Color = "#3b82f6" },
                            new() { Value = 4, Label = "故障", Color = "#ef4444" },
                            new() { Value = 3, Label = "报废", Color = "#6b7280" }
                        }
                    },
                    UpdateTime = DateTime.Now
                };
                
                return Ok(ApiResponse<AssetAnalyticsWorkbenchDto>.CreateSuccess(workbenchData, "获取资产分析工作台数据成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取工作台数据时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<AssetAnalyticsWorkbenchDto>("获取工作台数据时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取区域筛选选项（type=2的位置列表）
        /// </summary>
        /// <returns>区域选项列表</returns>
        [HttpGet("region-options")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<AssetRegionDto>>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<List<AssetRegionDto>>))]
        public async Task<IActionResult> GetRegionOptions()
        {
            try
            {
                _logger.LogInformation("开始获取区域筛选选项（type=2的位置）");
                var result = await _assetStatisticsService.GetRegionOptionsAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取区域选项时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<List<AssetRegionDto>>("获取区域选项时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取部门筛选选项（基于资产位置的部门关联）
        /// </summary>
        /// <returns>部门选项列表</returns>
        [HttpGet("department-options")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<AssetDepartmentDto>>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<List<AssetDepartmentDto>>))]
        public async Task<IActionResult> GetDepartmentOptions()
        {
            try
            {
                _logger.LogInformation("开始获取部门筛选选项（基于资产位置的部门关联）");
                var result = await _assetStatisticsService.GetDepartmentOptionsAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取部门选项时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<List<AssetDepartmentDto>>("获取部门选项时发生服务器错误: " + ex.Message));
            }
        }

    }
}