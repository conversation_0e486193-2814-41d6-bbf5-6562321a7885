// File: Domain/Entities/RepairItem.cs
// Description: 返厂维修单明细表实体，存储每个返修资产的详细信息

#nullable enable
using ItAssetsSystem.Models.Entities;

namespace ItAssetsSystem.Domain.Entities;

public class RepairItem
{
    public int Id { get; set; }
    public int RepairOrderId { get; set; }
    public int AssetId { get; set; }
    public int? FaultRecordId { get; set; }
    public string? Description { get; set; }
    public decimal RepairCost { get; set; }
    public int RepairStatus { get; set; }
    public string? RepairResult { get; set; }

    public virtual RepairOrder RepairOrder { get; set; } = null!;
    public virtual Asset Asset { get; set; } = null!;
    public virtual FaultRecord? FaultRecord { get; set; }
}