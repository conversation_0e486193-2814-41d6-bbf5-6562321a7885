// File: Domain/Entities/AssetSnapshot.cs
// Description: 资产历史数据快照实体，用于历史趋势分析和跨时间对比

#nullable enable
using System;

namespace ItAssetsSystem.Domain.Entities;

public class AssetSnapshot
{
    public long Id { get; set; }
    public DateTime SnapshotDate { get; set; }
    public int AssetId { get; set; }
    public string AssetCode { get; set; } = null!;
    public string? FinancialCode { get; set; }
    public string AssetName { get; set; } = null!;
    public int AssetTypeId { get; set; }
    public int? LocationId { get; set; }
    public int? DepartmentId { get; set; }
    public int Status { get; set; }
    public decimal? Price { get; set; }
    public DateTime? PurchaseDate { get; set; }
}