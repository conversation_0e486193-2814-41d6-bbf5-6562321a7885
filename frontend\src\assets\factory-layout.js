// File: frontend/src/assets/factory-layout.js
// Description: 厂区布局数据，用于资产分析工作台的热力图展示

/**
 * 厂区布局数据
 * coords: 区域坐标点，用于绘制多边形
 * value: 资产密度值，范围0-100
 * assetCount: 区域内资产数量
 * onlineRate: 设备在线率
 * utilizationRate: 设备利用率
 */
export const factoryAreas = {
  '生产车间': [
    { 
      name: '注塑区', 
      coords: [[50, 50], [150, 50], [150, 150], [50, 150]], 
      value: 85, 
      assetCount: 42, 
      onlineRate: 92, 
      utilizationRate: 78,
      assetTypes: {
        '注塑机': 18,
        '工控机': 12,
        '监控设备': 8,
        '其他设备': 4
      },
      valueDistribution: [
        { range: '0-10万', count: 5 },
        { range: '10-50万', count: 25 },
        { range: '50-100万', count: 10 },
        { range: '100万以上', count: 2 }
      ],
      maintenanceStatus: {
        '正常': 36,
        '待保养': 4,
        '维修中': 2
      }
    },
    { 
      name: '组装线A', 
      coords: [[170, 50], [270, 50], [270, 100], [170, 100]], 
      value: 65, 
      assetCount: 28, 
      onlineRate: 96, 
      utilizationRate: 82,
      assetTypes: {
        '装配设备': 14,
        '测试设备': 6,
        '工控机': 5,
        '其他设备': 3
      },
      valueDistribution: [
        { range: '0-10万', count: 8 },
        { range: '10-50万', count: 12 },
        { range: '50-100万', count: 6 },
        { range: '100万以上', count: 2 }
      ],
      maintenanceStatus: {
        '正常': 25,
        '待保养': 2,
        '维修中': 1
      }
    },
    { 
      name: '组装线B', 
      coords: [[170, 120], [270, 120], [270, 170], [170, 170]], 
      value: 72, 
      assetCount: 31, 
      onlineRate: 94, 
      utilizationRate: 79,
      assetTypes: {
        '装配设备': 16,
        '测试设备': 7,
        '工控机': 5,
        '其他设备': 3
      },
      valueDistribution: [
        { range: '0-10万', count: 7 },
        { range: '10-50万', count: 15 },
        { range: '50-100万', count: 7 },
        { range: '100万以上', count: 2 }
      ],
      maintenanceStatus: {
        '正常': 28,
        '待保养': 2,
        '维修中': 1
      }
    },
    { 
      name: '测试区', 
      coords: [[50, 170], [150, 170], [150, 270], [50, 270]], 
      value: 78, 
      assetCount: 35, 
      onlineRate: 97, 
      utilizationRate: 85,
      assetTypes: {
        '测试设备': 22,
        '工控机': 8,
        '监控设备': 3,
        '其他设备': 2
      },
      valueDistribution: [
        { range: '0-10万', count: 5 },
        { range: '10-50万', count: 18 },
        { range: '50-100万', count: 10 },
        { range: '100万以上', count: 2 }
      ],
      maintenanceStatus: {
        '正常': 32,
        '待保养': 2,
        '维修中': 1
      }
    },
    { 
      name: '包装区', 
      coords: [[170, 190], [270, 190], [270, 270], [170, 270]], 
      value: 45, 
      assetCount: 18, 
      onlineRate: 89, 
      utilizationRate: 72,
      assetTypes: {
        '包装设备': 10,
        '工控机': 4,
        '监控设备': 2,
        '其他设备': 2
      },
      valueDistribution: [
        { range: '0-10万', count: 10 },
        { range: '10-50万', count: 6 },
        { range: '50-100万', count: 2 },
        { range: '100万以上', count: 0 }
      ],
      maintenanceStatus: {
        '正常': 16,
        '待保养': 1,
        '维修中': 1
      }
    }
  ],
  '仓储区域': [
    { 
      name: '原料仓', 
      coords: [[50, 50], [200, 50], [200, 120], [50, 120]], 
      value: 35, 
      assetCount: 15, 
      onlineRate: 87, 
      utilizationRate: 65,
      assetTypes: {
        '叉车': 5,
        '监控设备': 6,
        '工控机': 2,
        '其他设备': 2
      },
      valueDistribution: [
        { range: '0-10万', count: 8 },
        { range: '10-50万', count: 5 },
        { range: '50-100万', count: 2 },
        { range: '100万以上', count: 0 }
      ],
      maintenanceStatus: {
        '正常': 13,
        '待保养': 1,
        '维修中': 1
      }
    },
    { 
      name: '成品仓', 
      coords: [[50, 140], [200, 140], [200, 230], [50, 230]], 
      value: 42, 
      assetCount: 18, 
      onlineRate: 92, 
      utilizationRate: 70,
      assetTypes: {
        '叉车': 6,
        '监控设备': 7,
        '工控机': 3,
        '其他设备': 2
      },
      valueDistribution: [
        { range: '0-10万', count: 10 },
        { range: '10-50万', count: 6 },
        { range: '50-100万', count: 2 },
        { range: '100万以上', count: 0 }
      ],
      maintenanceStatus: {
        '正常': 16,
        '待保养': 1,
        '维修中': 1
      }
    },
    { 
      name: '物流区', 
      coords: [[220, 50], [320, 50], [320, 230], [220, 230]], 
      value: 28, 
      assetCount: 12, 
      onlineRate: 83, 
      utilizationRate: 62,
      assetTypes: {
        '叉车': 4,
        '监控设备': 5,
        '工控机': 2,
        '其他设备': 1
      },
      valueDistribution: [
        { range: '0-10万', count: 7 },
        { range: '10-50万', count: 4 },
        { range: '50-100万', count: 1 },
        { range: '100万以上', count: 0 }
      ],
      maintenanceStatus: {
        '正常': 10,
        '待保养': 1,
        '维修中': 1
      }
    }
  ],
  '办公区域': [
    { 
      name: '研发中心', 
      coords: [[50, 50], [150, 50], [150, 150], [50, 150]], 
      value: 95, 
      assetCount: 68, 
      onlineRate: 98, 
      utilizationRate: 92,
      assetTypes: {
        '工作站': 32,
        '笔记本电脑': 24,
        '服务器': 8,
        '其他设备': 4
      },
      valueDistribution: [
        { range: '0-10万', count: 48 },
        { range: '10-50万', count: 15 },
        { range: '50-100万', count: 4 },
        { range: '100万以上', count: 1 }
      ],
      maintenanceStatus: {
        '正常': 65,
        '待保养': 2,
        '维修中': 1
      }
    },
    { 
      name: '行政办公区', 
      coords: [[170, 50], [300, 50], [300, 150], [170, 150]], 
      value: 75, 
      assetCount: 45, 
      onlineRate: 96, 
      utilizationRate: 88,
      assetTypes: {
        '台式电脑': 25,
        '笔记本电脑': 12,
        '打印设备': 5,
        '其他设备': 3
      },
      valueDistribution: [
        { range: '0-10万', count: 38 },
        { range: '10-50万', count: 6 },
        { range: '50-100万', count: 1 },
        { range: '100万以上', count: 0 }
      ],
      maintenanceStatus: {
        '正常': 42,
        '待保养': 2,
        '维修中': 1
      }
    },
    { 
      name: '会议中心', 
      coords: [[50, 170], [150, 170], [150, 270], [50, 270]], 
      value: 55, 
      assetCount: 24, 
      onlineRate: 95, 
      utilizationRate: 75,
      assetTypes: {
        '投影设备': 8,
        '会议系统': 6,
        '笔记本电脑': 5,
        '其他设备': 5
      },
      valueDistribution: [
        { range: '0-10万', count: 15 },
        { range: '10-50万', count: 7 },
        { range: '50-100万', count: 2 },
        { range: '100万以上', count: 0 }
      ],
      maintenanceStatus: {
        '正常': 22,
        '待保养': 1,
        '维修中': 1
      }
    },
    { 
      name: '数据中心', 
      coords: [[170, 170], [250, 170], [250, 270], [170, 270]], 
      value: 85, 
      assetCount: 36, 
      onlineRate: 99, 
      utilizationRate: 95,
      assetTypes: {
        '服务器': 24,
        '网络设备': 8,
        '存储设备': 3,
        '其他设备': 1
      },
      valueDistribution: [
        { range: '0-10万', count: 5 },
        { range: '10-50万', count: 18 },
        { range: '50-100万', count: 10 },
        { range: '100万以上', count: 3 }
      ],
      maintenanceStatus: {
        '正常': 35,
        '待保养': 1,
        '维修中': 0
      }
    }
  ]
}

/**
 * 获取热力图颜色
 * @param {number} value - 0-100之间的值
 * @param {boolean} isDark - 是否为暗色主题
 * @returns {string} 颜色值
 */
export function getHeatColor(value, isDark = true) {
  // 暗色主题颜色范围
  const darkColors = [
    { value: 0, color: 'rgba(0, 32, 96, 0.6)' },
    { value: 25, color: 'rgba(0, 64, 128, 0.7)' },
    { value: 50, color: 'rgba(0, 128, 192, 0.8)' },
    { value: 75, color: 'rgba(0, 192, 255, 0.9)' },
    { value: 100, color: 'rgba(64, 224, 255, 1.0)' }
  ]
  
  // 亮色主题颜色范围
  const lightColors = [
    { value: 0, color: 'rgba(240, 249, 255, 0.8)' },
    { value: 25, color: 'rgba(189, 229, 255, 0.8)' },
    { value: 50, color: 'rgba(107, 174, 214, 0.8)' },
    { value: 75, color: 'rgba(49, 130, 189, 0.8)' },
    { value: 100, color: 'rgba(8, 81, 156, 0.8)' }
  ]
  
  const colors = isDark ? darkColors : lightColors
  
  // 找到value所在的区间
  for (let i = 1; i < colors.length; i++) {
    if (value <= colors[i].value) {
      const lower = colors[i-1]
      const upper = colors[i]
      const range = upper.value - lower.value
      const valueInRange = value - lower.value
      const ratio = range === 0 ? 0 : valueInRange / range
      
      // 解析颜色
      const lowerColor = lower.color.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/)
      const upperColor = upper.color.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/)
      
      if (!lowerColor || !upperColor) return lower.color
      
      // 计算插值颜色
      const r = Math.round(parseInt(lowerColor[1]) + ratio * (parseInt(upperColor[1]) - parseInt(lowerColor[1])))
      const g = Math.round(parseInt(lowerColor[2]) + ratio * (parseInt(upperColor[2]) - parseInt(lowerColor[2])))
      const b = Math.round(parseInt(lowerColor[3]) + ratio * (parseInt(upperColor[3]) - parseInt(lowerColor[3])))
      const a = parseFloat(lowerColor[4]) + ratio * (parseFloat(upperColor[4]) - parseFloat(lowerColor[4]))
      
      return `rgba(${r}, ${g}, ${b}, ${a.toFixed(2)})`
    }
  }
  
  return colors[colors.length - 1].color
}

/**
 * 时段分析数据
 * 按小时统计的设备使用情况
 */
export const timeSeriesData = {
  // 时间点（24小时制）
  hours: ['00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00', '07:00', '08:00', '09:00', '10:00', '11:00', 
          '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00', '23:00'],
  
  // 工作日数据
  workday: [15, 12, 10, 8, 5, 8, 20, 45, 80, 88, 90, 85, 45, 75, 82, 85, 80, 70, 55, 35, 30, 25, 20, 18],
  
  // 周末数据
  weekend: [10, 8, 5, 3, 2, 3, 5, 10, 20, 30, 42, 45, 40, 38, 35, 30, 25, 20, 18, 25, 30, 28, 20, 15],
  
  // 按区域的时段数据
  areas: {
    '生产车间': {
      workday: [40, 38, 35, 30, 28, 30, 45, 60, 85, 90, 92, 88, 50, 80, 85, 90, 88, 75, 65, 50, 45, 42, 40, 38],
      weekend: [30, 28, 25, 20, 18, 20, 25, 30, 35, 40, 45, 48, 40, 42, 45, 48, 45, 40, 35, 30, 32, 35, 32, 30]
    },
    '仓储区域': {
      workday: [20, 18, 15, 12, 10, 15, 25, 50, 75, 80, 85, 80, 40, 70, 75, 80, 75, 65, 50, 30, 25, 20, 18, 15],
      weekend: [10, 8, 5, 3, 2, 3, 5, 10, 20, 30, 35, 40, 35, 30, 28, 25, 20, 15, 10, 15, 20, 18, 15, 10]
    },
    '办公区域': {
      workday: [5, 3, 2, 1, 1, 2, 10, 40, 85, 95, 98, 95, 50, 85, 90, 92, 85, 70, 40, 20, 15, 10, 8, 5],
      weekend: [2, 1, 1, 0, 0, 0, 1, 5, 10, 15, 20, 25, 20, 18, 15, 12, 10, 8, 5, 10, 15, 12, 8, 5]
    }
  },
  
  // 按资产类型的时段数据
  assetTypes: {
    '工控机': {
      workday: [80, 78, 75, 72, 70, 72, 78, 85, 90, 92, 95, 92, 85, 90, 92, 95, 92, 88, 85, 80, 82, 80, 78, 75],
      weekend: [70, 68, 65, 60, 58, 60, 65, 70, 75, 78, 80, 82, 78, 75, 72, 70, 68, 65, 62, 65, 68, 65, 62, 60]
    },
    '测试设备': {
      workday: [10, 8, 5, 3, 2, 5, 15, 50, 85, 90, 95, 90, 45, 85, 90, 92, 88, 75, 50, 30, 25, 20, 15, 12],
      weekend: [5, 3, 2, 1, 0, 1, 2, 8, 15, 25, 35, 40, 35, 30, 25, 20, 15, 10, 8, 12, 18, 15, 10, 8]
    },
    '办公设备': {
      workday: [5, 3, 2, 1, 1, 2, 10, 45, 90, 95, 98, 95, 50, 90, 95, 98, 95, 80, 50, 25, 20, 15, 10, 8],
      weekend: [2, 1, 0, 0, 0, 0, 1, 5, 12, 20, 25, 28, 25, 20, 18, 15, 10, 8, 5, 8, 12, 10, 8, 5]
    }
  }
}

/**
 * 资产价值区间分布数据
 */
export const assetValueDistribution = {
  ranges: ['0-10万', '10-50万', '50-100万', '100万以上'],
  data: [
    { name: '生产设备', values: [45, 120, 85, 30] },
    { name: '办公设备', values: [180, 45, 10, 5] },
    { name: '测试设备', values: [35, 65, 40, 15] },
    { name: '仓储设备', values: [55, 30, 15, 5] }
  ]
}

/**
 * 资产状态分布数据
 */
export const assetStatusDistribution = {
  statuses: ['在用', '闲置', '维修中', '报废'],
  departments: ['研发部', '生产部', '质量部', '行政部', '销售部'],
  data: [
    [80, 10, 5, 5],  // 研发部
    [75, 15, 8, 2],  // 生产部
    [85, 8, 5, 2],   // 质量部
    [70, 20, 5, 5],  // 行政部
    [65, 25, 5, 5]   // 销售部
  ]
}

/**
 * 部门资产矩阵数据
 */
export const departmentAssetMatrix = {
  departments: ['研发部', '生产部', '质量部', '行政部', '销售部'],
  assetTypes: ['计算机设备', '测试设备', '生产设备', '办公设备', '网络设备'],
  data: [
    [120, 45, 10, 35, 25],  // 研发部
    [35, 30, 150, 20, 15],  // 生产部
    [25, 85, 30, 15, 10],   // 质量部
    [40, 5, 5, 85, 20],     // 行政部
    [45, 5, 5, 30, 15]      // 销售部
  ]
} 