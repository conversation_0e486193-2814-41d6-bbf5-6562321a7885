// File: Application/Features/Statistics/Dtos/DynamicStatisticsQueryDto.cs
// Description: 动态统计查询请求DTO，支持灵活的维度组合和筛选条件

#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.Statistics.Dtos;

/// <summary>
/// 动态统计查询请求DTO
/// </summary>
public class DynamicStatisticsQueryDto
{
    /// <summary>
    /// 分析维度 (department, assetType, location, status等)
    /// </summary>
    [Required]
    public string Dimension { get; set; } = null!;

    /// <summary>
    /// 度量指标 (count, value, averageValue等)
    /// </summary>
    [Required]
    public string Metric { get; set; } = null!;

    /// <summary>
    /// 筛选条件
    /// </summary>
    public Dictionary<string, object> Filters { get; set; } = new();

    /// <summary>
    /// 时间范围筛选
    /// </summary>
    public DateRangeFilterDto? DateRange { get; set; }

    /// <summary>
    /// 分组维度 (用于二级分组)
    /// </summary>
    public string? GroupBy { get; set; }

    /// <summary>
    /// 排序字段
    /// </summary>
    public string? OrderBy { get; set; }

    /// <summary>
    /// 排序方向 (asc, desc)
    /// </summary>
    public string OrderDirection { get; set; } = "desc";

    /// <summary>
    /// 分页参数
    /// </summary>
    public PaginationDto? Pagination { get; set; }
}

/// <summary>
/// 时间范围筛选DTO
/// </summary>
public class DateRangeFilterDto
{
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string? Field { get; set; } = "CreatedAt";
}

/// <summary>
/// 分页参数DTO
/// </summary>
public class PaginationDto
{
    public int Page { get; set; } = 1;
    public int Size { get; set; } = 20;
}