using System.Collections.Generic;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.AssetStatistics.Dtos;

namespace ItAssetsSystem.Application.Features.AssetStatistics.Services
{
    /// <summary>
    /// 资产统计服务接口
    /// </summary>
    public interface IAssetStatisticsService
    {
        /// <summary>
        /// 获取资产总体统计
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>总体统计数据</returns>
        Task<ApiResponse<AssetOverallStatisticsDto>> GetOverallStatisticsAsync(AssetStatisticsQueryDto query);

        /// <summary>
        /// 获取按资产类型统计
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>按类型统计数据</returns>
        Task<ApiResponse<List<AssetTypeStatisticsDto>>> GetStatisticsByTypeAsync(AssetStatisticsQueryDto query);

        /// <summary>
        /// 获取按区域统计
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>按区域统计数据</returns>
        Task<ApiResponse<List<AssetRegionStatisticsDto>>> GetStatisticsByRegionAsync(AssetStatisticsQueryDto query);

        /// <summary>
        /// 获取按部门统计
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>按部门统计数据</returns>
        Task<ApiResponse<List<AssetDepartmentStatisticsDto>>> GetStatisticsByDepartmentAsync(AssetStatisticsQueryDto query);

        /// <summary>
        /// 获取周趋势数据
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>周趋势数据</returns>
        Task<ApiResponse<List<AssetTrendDataDto>>> GetWeeklyTrendAsync(AssetTrendQueryDto query);

        /// <summary>
        /// 获取日趋势数据
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>日趋势数据</returns>
        Task<ApiResponse<List<AssetTrendDataDto>>> GetDailyTrendAsync(AssetTrendQueryDto query);

        /// <summary>
        /// 获取月趋势数据
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>月趋势数据</returns>
        Task<ApiResponse<List<AssetTrendDataDto>>> GetMonthlyTrendAsync(AssetTrendQueryDto query);

        /// <summary>
        /// 获取组合统计数据
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>组合统计数据</returns>
        Task<ApiResponse<AssetCombinedStatisticsDto>> GetCombinedStatisticsAsync(AssetStatisticsQueryDto query);

        /// <summary>
        /// 获取资产类型列表
        /// </summary>
        /// <returns>资产类型列表</returns>
        Task<ApiResponse<List<AssetTypeDto>>> GetAssetTypesAsync();

        /// <summary>
        /// 获取区域列表
        /// </summary>
        /// <returns>区域列表</returns>
        Task<ApiResponse<List<AssetRegionDto>>> GetRegionsAsync();

        /// <summary>
        /// 获取部门列表
        /// </summary>
        /// <returns>部门列表</returns>
        Task<ApiResponse<List<AssetDepartmentDto>>> GetDepartmentsAsync();

        /// <summary>
        /// 获取区域筛选选项（type=2的位置列表）
        /// </summary>
        /// <returns>区域选项列表</returns>
        Task<ApiResponse<List<AssetRegionDto>>> GetRegionOptionsAsync();

        /// <summary>
        /// 获取部门筛选选项（基于资产位置的部门关联）
        /// </summary>
        /// <returns>部门选项列表</returns>
        Task<ApiResponse<List<AssetDepartmentDto>>> GetDepartmentOptionsAsync();

        /// <summary>
        /// 获取资产分析工作台的完整数据（首次加载用）
        /// 包含：KPI指标、各维度统计、图表数据、筛选选项等
        /// </summary>
        /// <returns>完整的工作台数据</returns>
        Task<ApiResponse<AssetAnalyticsWorkbenchDto>> GetAnalyticsWorkbenchDataAsync();
    }
}