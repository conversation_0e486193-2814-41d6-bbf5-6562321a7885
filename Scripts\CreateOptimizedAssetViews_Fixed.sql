-- IT资产管理系统 - 优化版视图创建脚本 (修复版)
-- 版本: v2.1 (语法错误修复版)
-- 创建时间: 2025-01-08
-- 修复内容: 语法错误、循环依赖、字段引用问题

-- ============================================================================
-- 0. 创建配置表和辅助结构
-- ============================================================================

-- 价值区间配置表
DROP TABLE IF EXISTS asset_value_ranges;

CREATE TABLE asset_value_ranges (
    id INT PRIMARY KEY AUTO_INCREMENT,
    min_value DECIMAL(18,2) NOT NULL,
    max_value DECIMAL(18,2) NOT NULL,
    range_label VARCHAR(20) NOT NULL,
    range_color VARCHAR(10) DEFAULT '#3b82f6',
    sort_order INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产价值区间配置表';

-- 插入默认价值区间配置
INSERT INTO asset_value_ranges (min_value, max_value, range_label, range_color, sort_order) VALUES 
(0, 10000, '0-1万', '#10b981', 1),
(10000, 50000, '1-5万', '#3b82f6', 2),
(50000, 100000, '5-10万', '#f59e0b', 3),
(100000, 500000, '10-50万', '#ef4444', 4),
(500000, 999999999, '50万以上', '#8b5cf6', 5);

-- 视图性能统计表
DROP TABLE IF EXISTS view_performance_stats;

CREATE TABLE view_performance_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    view_name VARCHAR(64) NOT NULL,
    query_type VARCHAR(32) NOT NULL,
    execution_time_ms DECIMAL(10,3) NOT NULL,
    record_count INT DEFAULT 0,
    query_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_view_date (view_name, query_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视图性能统计表';

-- ============================================================================
-- 1. 核心资产增强视图 (v_assets_enhanced) - 完整修复版
-- ============================================================================

DROP VIEW IF EXISTS v_assets_enhanced;

CREATE VIEW v_assets_enhanced AS
SELECT 
    -- ================== 资产基础信息 ==================
    a.Id AS AssetId,
    a.assetCode AS AssetCode,
    a.FinancialCode,
    a.Name AS AssetName,
    a.Status,
    COALESCE(a.Price, 0) AS Price,
    a.PurchaseDate,
    a.WarrantyExpireDate,
    a.SerialNumber,
    a.Model,
    a.Brand,
    a.Notes,
    a.CreatedAt,
    a.UpdatedAt,
    
    -- ================== 资产类型信息 ==================
    a.AssetTypeId,
    COALESCE(at.Name, '未分类') AS AssetTypeName,
    COALESCE(at.Code, 'UNKNOWN') AS AssetTypeCode,
    COALESCE(at.Description, '') AS AssetTypeDescription,
    
    -- ================== 当前位置信息 ==================
    a.LocationId AS CurrentLocationId,
    COALESCE(l.Name, '未指定位置') AS CurrentLocationName,
    COALESCE(l.Code, '') AS CurrentLocationCode,
    COALESCE(l.Type, -1) AS LocationType,
    COALESCE(l.Path, CAST(a.LocationId AS CHAR)) AS LocationPath,
    
    -- ================== 优化的位置层级解析 ==================
    CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 1), ',', -1) AS UNSIGNED) AS Level1LocationId,
    CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 2), ',', -1) AS UNSIGNED) AS Level2LocationId,
    CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 3), ',', -1) AS UNSIGNED) AS Level3LocationId,
    CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 4), ',', -1) AS UNSIGNED) AS Level4LocationId,
    CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 5), ',', -1) AS UNSIGNED) AS Level5LocationId,
    
    -- ================== 智能部门继承逻辑 ==================
    COALESCE(
        l.DefaultDepartmentId,
        l2.DefaultDepartmentId,
        l3.DefaultDepartmentId,
        l4.DefaultDepartmentId,
        l5.DefaultDepartmentId
    ) AS InheritedDepartmentId,
    
    -- 部门继承来源追踪
    CASE 
        WHEN l.DefaultDepartmentId IS NOT NULL THEN 'current'
        WHEN l2.DefaultDepartmentId IS NOT NULL THEN 'level2'
        WHEN l3.DefaultDepartmentId IS NOT NULL THEN 'level3'
        WHEN l4.DefaultDepartmentId IS NOT NULL THEN 'level4'
        WHEN l5.DefaultDepartmentId IS NOT NULL THEN 'level5'
        ELSE 'none'
    END AS DepartmentSource,
    
    -- ================== 部门信息 ==================
    COALESCE(d.Id, 0) AS DepartmentId,
    COALESCE(d.Name, '未分配部门') AS DepartmentName,
    COALESCE(d.Code, '') AS DepartmentCode,
    d.ManagerId AS DepartmentManagerId,
    d.DeputyManagerId AS DepartmentDeputyManagerId,
    COALESCE(d.Path, '') AS DepartmentPath,
    
    -- ================== 负责人信息 ==================
    COALESCE(mgr.Username, '') AS DepartmentManagerName,
    COALESCE(deputy.Username, '') AS DepartmentDeputyManagerName,
    COALESCE(resp.Username, '') AS LocationResponsiblePersonName,
    
    -- ================== 区域信息 ==================
    CASE 
        WHEN l.Path IS NOT NULL AND CHAR_LENGTH(l.Path) - CHAR_LENGTH(REPLACE(l.Path, ',', '')) >= 3 THEN
            CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(l.Path, ',', 4), ',', -1) AS UNSIGNED)
        ELSE COALESCE(a.LocationId, 0)
    END AS RegionId,
    
    COALESCE(region_loc.Name, l.Name, '未知区域') AS RegionName,
    
    -- ================== 动态价值区间 ==================
    COALESCE(vr.range_label, '未知') AS ValueRange,
    COALESCE(vr.range_color, '#6b7280') AS ValueRangeColor,
    COALESCE(vr.sort_order, 999) AS ValueRangeSortOrder,
    
    -- ================== 统计用计算字段 ==================
    CASE a.Status
        WHEN 0 THEN '闲置'
        WHEN 1 THEN '在用'
        WHEN 2 THEN '维修中'
        WHEN 3 THEN '报废'
        WHEN 4 THEN '故障'
        ELSE '未知'
    END AS StatusText,
    
    CASE a.Status
        WHEN 0 THEN 'idle'
        WHEN 1 THEN 'in_use'
        WHEN 2 THEN 'maintenance'
        WHEN 3 THEN 'scrapped'
        WHEN 4 THEN 'faulty'
        ELSE 'unknown'
    END AS StatusCategory,
    
    -- 价值（万元）
    ROUND(COALESCE(a.Price, 0) / 10000, 2) AS ValueInWan,
    
    -- 布尔标识字段（用于快速统计）
    CASE WHEN a.Status = 1 THEN 1 ELSE 0 END AS IsInUse,
    CASE WHEN a.Status = 0 THEN 1 ELSE 0 END AS IsIdle,
    CASE WHEN a.Status = 2 THEN 1 ELSE 0 END AS IsMaintenance,
    CASE WHEN a.Status = 4 THEN 1 ELSE 0 END AS IsFaulty,
    CASE WHEN a.Status = 3 THEN 1 ELSE 0 END AS IsScrapped,
    
    -- 时间相关字段
    YEAR(a.CreatedAt) AS CreatedYear,
    MONTH(a.CreatedAt) AS CreatedMonth,
    DATE(a.CreatedAt) AS CreatedDate,
    
    -- 资产年龄（月）
    TIMESTAMPDIFF(MONTH, a.CreatedAt, NOW()) AS AssetAgeMonths

FROM assets a

-- ================== 主表关联 ==================
LEFT JOIN assettypes at ON a.AssetTypeId = at.Id
LEFT JOIN locations l ON a.LocationId = l.Id

-- ================== 位置层级关联 ==================
LEFT JOIN locations l2 ON l2.Id = CAST(
    SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 2), ',', -1) AS UNSIGNED)

LEFT JOIN locations l3 ON l3.Id = CAST(
    SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 3), ',', -1) AS UNSIGNED)

LEFT JOIN locations l4 ON l4.Id = CAST(
    SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 4), ',', -1) AS UNSIGNED)

LEFT JOIN locations l5 ON l5.Id = CAST(
    SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(l.Path, '0'), ',0,0,0,0,0'), ',', 5), ',', -1) AS UNSIGNED)

-- 区域位置信息
LEFT JOIN locations region_loc ON region_loc.Id = (
    CASE 
        WHEN l.Path IS NOT NULL AND CHAR_LENGTH(l.Path) - CHAR_LENGTH(REPLACE(l.Path, ',', '')) >= 3 THEN
            CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(l.Path, ',', 4), ',', -1) AS UNSIGNED)
        ELSE a.LocationId
    END
)

-- ================== 部门和负责人关联 ==================
LEFT JOIN departments d ON d.Id = COALESCE(
    l.DefaultDepartmentId,
    l2.DefaultDepartmentId,
    l3.DefaultDepartmentId,
    l4.DefaultDepartmentId,
    l5.DefaultDepartmentId
)

LEFT JOIN users mgr ON mgr.Id = d.ManagerId
LEFT JOIN users deputy ON deputy.Id = d.DeputyManagerId
LEFT JOIN users resp ON resp.Id = l.DefaultResponsiblePersonId

-- ================== 价值区间关联 ==================
LEFT JOIN asset_value_ranges vr ON (
    COALESCE(a.Price, 0) >= vr.min_value 
    AND COALESCE(a.Price, 0) < vr.max_value 
    AND vr.is_active = TRUE
);

-- ============================================================================
-- 2. KPI汇总视图 (v_asset_kpi_enhanced) - 修复版
-- ============================================================================

DROP VIEW IF EXISTS v_asset_kpi_enhanced;

CREATE VIEW v_asset_kpi_enhanced AS
SELECT 
    -- ================== 基础KPI ==================
    COUNT(*) AS TotalAssets,  -- 总资产数量
    SUM(Price) AS TotalValue,  -- 总价值
    ROUND(SUM(Price) / 10000, 2) AS TotalValueWan,  -- 总价值（万元）
    
    -- ================== 各状态数量 ==================
    SUM(IsInUse) AS InUseAssets,  -- 在用资产数量
    SUM(IsIdle) AS IdleAssets,  -- 闲置资产数量
    SUM(IsMaintenance) AS MaintenanceAssets,  -- 维护中资产数量
    SUM(IsFaulty) AS FaultAssets,  -- 故障资产数量
    SUM(IsScrapped) AS ScrappedAssets,  -- 报废资产数量
    
    -- ================== 各状态比率 ==================
    ROUND(SUM(IsInUse) * 100.0 / COUNT(*), 2) AS InUseRate,  -- 在用率（%）
    ROUND(SUM(IsIdle) * 100.0 / COUNT(*), 2) AS IdleRate,  -- 闲置率（%）
    ROUND(SUM(IsMaintenance) * 100.0 / COUNT(*), 2) AS MaintenanceRate,  -- 维护率（%）
    ROUND(SUM(IsFaulty) * 100.0 / COUNT(*), 2) AS FaultRate,  -- 故障率（%）
    ROUND(SUM(IsScrapped) * 100.0 / COUNT(*), 2) AS ScrappedRate,  -- 报废率（%）
    
    -- ================== 计算的KPI ==================
    -- 平均利用率 = 在用率 - (空闲率*0.3) - (维修率*0.5)
    ROUND(
        SUM(IsInUse) * 100.0 / COUNT(*) - 
        SUM(IsIdle) * 30.0 / COUNT(*) - 
        SUM(IsMaintenance) * 50.0 / COUNT(*), 
        2
    ) AS AverageUtilization,  -- 平均利用率（%）
    
    -- 健康度 = 100 - 故障率 - 报废率
    ROUND(
        100 - SUM(IsFaulty) * 100.0 / COUNT(*) - SUM(IsScrapped) * 100.0 / COUNT(*), 
        2
    ) AS HealthScore,  -- 健康度（%）
    
    -- ================== 统计信息 ==================
    COUNT(DISTINCT AssetTypeId) AS TypeCount,  -- 资产类型数量
    COUNT(DISTINCT CASE WHEN InheritedDepartmentId > 0 THEN InheritedDepartmentId END) AS DepartmentCount,  -- 承属部门数量（排除0值）
    COUNT(DISTINCT RegionId) AS RegionCount,  -- 区域数量
    
    -- ================== 时间信息 ==================
    MIN(CreatedAt) AS EarliestAsset,  -- 最早资产创建时间
    MAX(CreatedAt) AS LatestAsset,  -- 最新资产创建时间
    ROUND(AVG(AssetAgeMonths), 1) AS AverageAgeMonths,  -- 平均资产年龄（月）
    
    -- ================== 价值分析 ==================
    ROUND(AVG(Price), 2) AS AverageValue,  -- 平均资产价值
    MAX(Price) AS MaxAssetValue,  -- 最高资产价值
    MIN(CASE WHEN Price > 0 THEN Price END) AS MinAssetValue,  -- 最低有效资产价值（排除0或负值）
    
    -- ================== 趋势数据 ==================
    (SELECT COUNT(*) FROM assets a_sub WHERE a_sub.CreatedAt >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)) AS NewAssetsLast30Days,  -- 最近30天新增资产
    (SELECT COUNT(*) FROM assets a_sub WHERE a_sub.CreatedAt >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)) AS NewAssetsLast7Days  -- 最近7天新增资产

FROM v_assets_enhanced;

-- ============================================================================
-- 3. 高性能统计汇总视图 (v_asset_statistics_fast) - 修复版
-- ============================================================================

DROP VIEW IF EXISTS v_asset_statistics_fast;

CREATE VIEW v_asset_statistics_fast AS

-- 按资产类型统计
SELECT 
    'type' AS DimensionType,
    CAST(AssetTypeId AS CHAR) AS DimensionKey,
    AssetTypeName AS DimensionName,
    AssetTypeCode AS DimensionCode,
    NULL AS ParentKey,
    COUNT(*) AS TotalCount,
    SUM(IsInUse) AS InUseCount,
    SUM(IsIdle) AS IdleCount,
    SUM(IsMaintenance) AS MaintenanceCount,
    SUM(IsFaulty) AS FaultCount,
    SUM(IsScrapped) AS ScrappedCount,
    SUM(Price) AS TotalValue,
    ROUND(AVG(Price), 2) AS AverageValue,
    ROUND(SUM(IsInUse) * 100.0 / COUNT(*), 2) AS InUseRate,
    ROUND(SUM(IsFaulty) * 100.0 / COUNT(*), 2) AS FaultRate,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM assets), 2) AS Percentage,
    MIN(CreatedAt) AS EarliestCreated,
    MAX(CreatedAt) AS LatestCreated,
    COUNT(DISTINCT DepartmentId) AS DepartmentCount,
    COUNT(DISTINCT RegionId) AS RegionCount
FROM v_assets_enhanced
GROUP BY AssetTypeId, AssetTypeName, AssetTypeCode

UNION ALL

-- 按部门统计
SELECT 
    'department' AS DimensionType,
    CAST(InheritedDepartmentId AS CHAR) AS DimensionKey,
    DepartmentName AS DimensionName,
    DepartmentCode AS DimensionCode,
    NULL AS ParentKey,
    COUNT(*) AS TotalCount,
    SUM(IsInUse) AS InUseCount,
    SUM(IsIdle) AS IdleCount,
    SUM(IsMaintenance) AS MaintenanceCount,
    SUM(IsFaulty) AS FaultCount,
    SUM(IsScrapped) AS ScrappedCount,
    SUM(Price) AS TotalValue,
    ROUND(AVG(Price), 2) AS AverageValue,
    ROUND(SUM(IsInUse) * 100.0 / COUNT(*), 2) AS InUseRate,
    ROUND(SUM(IsFaulty) * 100.0 / COUNT(*), 2) AS FaultRate,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM assets), 2) AS Percentage,
    MIN(CreatedAt) AS EarliestCreated,
    MAX(CreatedAt) AS LatestCreated,
    COUNT(DISTINCT AssetTypeId) AS DepartmentCount,
    COUNT(DISTINCT RegionId) AS RegionCount
FROM v_assets_enhanced
WHERE InheritedDepartmentId IS NOT NULL AND InheritedDepartmentId > 0
GROUP BY InheritedDepartmentId, DepartmentName, DepartmentCode

UNION ALL

-- 按区域统计
SELECT 
    'region' AS DimensionType,
    CAST(RegionId AS CHAR) AS DimensionKey,
    RegionName AS DimensionName,
    '' AS DimensionCode,
    NULL AS ParentKey,
    COUNT(*) AS TotalCount,
    SUM(IsInUse) AS InUseCount,
    SUM(IsIdle) AS IdleCount,
    SUM(IsMaintenance) AS MaintenanceCount,
    SUM(IsFaulty) AS FaultCount,
    SUM(IsScrapped) AS ScrappedCount,
    SUM(Price) AS TotalValue,
    ROUND(AVG(Price), 2) AS AverageValue,
    ROUND(SUM(IsInUse) * 100.0 / COUNT(*), 2) AS InUseRate,
    ROUND(SUM(IsFaulty) * 100.0 / COUNT(*), 2) AS FaultRate,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM assets), 2) AS Percentage,
    MIN(CreatedAt) AS EarliestCreated,
    MAX(CreatedAt) AS LatestCreated,
    COUNT(DISTINCT AssetTypeId) AS DepartmentCount,
    COUNT(DISTINCT InheritedDepartmentId) AS RegionCount
FROM v_assets_enhanced
GROUP BY RegionId, RegionName

UNION ALL

-- 按状态统计
SELECT 
    'status' AS DimensionType,
    CAST(Status AS CHAR) AS DimensionKey,
    StatusText AS DimensionName,
    StatusCategory AS DimensionCode,
    NULL AS ParentKey,
    COUNT(*) AS TotalCount,
    SUM(IsInUse) AS InUseCount,
    SUM(IsIdle) AS IdleCount,
    SUM(IsMaintenance) AS MaintenanceCount,
    SUM(IsFaulty) AS FaultCount,
    SUM(IsScrapped) AS ScrappedCount,
    SUM(Price) AS TotalValue,
    ROUND(AVG(Price), 2) AS AverageValue,
    ROUND(SUM(IsInUse) * 100.0 / COUNT(*), 2) AS InUseRate,
    ROUND(SUM(IsFaulty) * 100.0 / COUNT(*), 2) AS FaultRate,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM assets), 2) AS Percentage,
    MIN(CreatedAt) AS EarliestCreated,
    MAX(CreatedAt) AS LatestCreated,
    COUNT(DISTINCT AssetTypeId) AS DepartmentCount,
    COUNT(DISTINCT InheritedDepartmentId) AS RegionCount
FROM v_assets_enhanced
GROUP BY Status, StatusText, StatusCategory;

-- ============================================================================
-- 4. 价值分布增强视图 (v_asset_value_distribution_enhanced) - 修复版
-- ============================================================================

DROP VIEW IF EXISTS v_asset_value_distribution_enhanced;

CREATE VIEW v_asset_value_distribution_enhanced AS
SELECT 
    vr.range_label AS ValueRange,
    vr.range_color AS RangeColor,
    vr.sort_order AS SortOrder,
    COALESCE(asset_stats.AssetCount, 0) AS AssetCount,
    COALESCE(asset_stats.TotalValue, 0) AS TotalValue,
    COALESCE(asset_stats.TotalValueWan, 0) AS TotalValueWan,
    COALESCE(asset_stats.AverageValue, 0) AS AverageValue,
    COALESCE(asset_stats.InUseCount, 0) AS InUseCount,
    COALESCE(asset_stats.FaultCount, 0) AS FaultCount,
    COALESCE(asset_stats.Percentage, 0) AS Percentage

FROM asset_value_ranges vr

LEFT JOIN (
    SELECT 
        ValueRange,
        COUNT(*) AS AssetCount,
        SUM(Price) AS TotalValue,
        ROUND(SUM(Price) / 10000, 2) AS TotalValueWan,
        ROUND(AVG(Price), 2) AS AverageValue,
        SUM(IsInUse) AS InUseCount,
        SUM(IsFaulty) AS FaultCount,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM assets), 2) AS Percentage
    FROM v_assets_enhanced
    GROUP BY ValueRange
) asset_stats ON vr.range_label = asset_stats.ValueRange

WHERE vr.is_active = TRUE
ORDER BY vr.sort_order;

-- ============================================================================
-- 5. 简化版物化视图表
-- ============================================================================

-- 物化视图：资产统计快照
DROP TABLE IF EXISTS mv_asset_statistics;
CREATE TABLE mv_asset_statistics (
    dimension_type VARCHAR(20) NOT NULL,
    dimension_key VARCHAR(50) NOT NULL,
    dimension_name VARCHAR(100) NOT NULL,
    total_count INT DEFAULT 0,
    in_use_count INT DEFAULT 0,
    fault_count INT DEFAULT 0,
    total_value DECIMAL(18,2) DEFAULT 0,
    in_use_rate DECIMAL(5,2) DEFAULT 0,
    fault_rate DECIMAL(5,2) DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (dimension_type, dimension_key),
    INDEX idx_type_updated (dimension_type, last_updated)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产统计快照表';

-- 物化视图：KPI快照
DROP TABLE IF EXISTS mv_asset_kpi;
CREATE TABLE mv_asset_kpi (
    id INT PRIMARY KEY AUTO_INCREMENT,
    total_assets INT DEFAULT 0,
    total_value DECIMAL(18,2) DEFAULT 0,
    in_use_rate DECIMAL(5,2) DEFAULT 0,
    fault_rate DECIMAL(5,2) DEFAULT 0,
    idle_rate DECIMAL(5,2) DEFAULT 0,
    maintenance_rate DECIMAL(5,2) DEFAULT 0,
    average_utilization DECIMAL(5,2) DEFAULT 0,
    health_score DECIMAL(5,2) DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_updated (last_updated)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='KPI指标快照表';

-- ============================================================================
-- 6. 数据刷新存储过程
-- ============================================================================

DELIMITER //

-- 刷新统计数据存储过程
DROP PROCEDURE IF EXISTS RefreshAssetStatistics//
CREATE PROCEDURE RefreshAssetStatistics()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;
    
    DELETE FROM mv_asset_statistics;
    
    INSERT INTO mv_asset_statistics (
        dimension_type, dimension_key, dimension_name, total_count, 
        in_use_count, fault_count, total_value, in_use_rate, fault_rate
    )
    SELECT 
        DimensionType, DimensionKey, DimensionName, TotalCount,
        InUseCount, FaultCount, TotalValue, InUseRate, FaultRate
    FROM v_asset_statistics_fast;
    
    COMMIT;
    
    SELECT CONCAT('资产统计数据刷新完成，处理 ', ROW_COUNT(), ' 条记录') AS result;
END//

-- 刷新KPI数据存储过程
DROP PROCEDURE IF EXISTS RefreshAssetKPI//
CREATE PROCEDURE RefreshAssetKPI()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;
    
    DELETE FROM mv_asset_kpi;
    
    INSERT INTO mv_asset_kpi (
        total_assets, total_value, in_use_rate, fault_rate, 
        idle_rate, maintenance_rate, average_utilization, health_score
    )
    SELECT 
        TotalAssets, TotalValue, InUseRate, FaultRate,
        IdleRate, MaintenanceRate, AverageUtilization, HealthScore
    FROM v_asset_kpi_enhanced;
    
    COMMIT;
    
    SELECT CONCAT('KPI数据刷新完成') AS result;
END//

DELIMITER ;

-- ============================================================================
-- 7. 初始数据加载和验证
-- ============================================================================

-- 初始化物化视图数据
CALL RefreshAssetStatistics();
CALL RefreshAssetKPI();

-- 验证视图数据
SELECT 'Views Validation' AS test_type;
SELECT COUNT(*) AS enhanced_view_count FROM v_assets_enhanced;
SELECT COUNT(*) AS kpi_view_count FROM v_asset_kpi_enhanced;
SELECT COUNT(*) AS statistics_view_count FROM v_asset_statistics_fast;
SELECT COUNT(*) AS value_distribution_count FROM v_asset_value_distribution_enhanced;

SELECT 'Materialized Tables Validation' AS test_type;
SELECT COUNT(*) AS mv_statistics_count FROM mv_asset_statistics;
SELECT COUNT(*) AS mv_kpi_count FROM mv_asset_kpi;

SELECT 'Script execution completed successfully!' AS result;