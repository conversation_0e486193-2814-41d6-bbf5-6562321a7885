// File: Application/Features/Statistics/Dtos/DynamicStatisticsResultDto.cs
// Description: 动态统计查询结果DTO，统一返回格式支持图表和表格展示

#nullable enable
using System.Collections.Generic;

namespace ItAssetsSystem.Application.Features.Statistics.Dtos;

/// <summary>
/// 动态统计查询结果DTO
/// </summary>
public class DynamicStatisticsResultDto
{
    /// <summary>
    /// 查询摘要信息
    /// </summary>
    public StatisticsSummaryDto Summary { get; set; } = null!;

    /// <summary>
    /// 聚合数据 (用于图表展示)
    /// </summary>
    public List<AggregatedDataItemDto> AggregatedData { get; set; } = new();

    /// <summary>
    /// 详细数据 (用于表格展示)
    /// </summary>
    public List<Dictionary<string, object>> DetailedData { get; set; } = new();

    /// <summary>
    /// 分页信息
    /// </summary>
    public PaginationMetadataDto? Pagination { get; set; }

    /// <summary>
    /// 执行统计信息
    /// </summary>
    public ExecutionMetadataDto ExecutionMetadata { get; set; } = null!;
}

/// <summary>
/// 统计摘要DTO
/// </summary>
public class StatisticsSummaryDto
{
    /// <summary>
    /// 总记录数
    /// </summary>
    public int TotalRecords { get; set; }

    /// <summary>
    /// 总值
    /// </summary>
    public decimal TotalValue { get; set; }

    /// <summary>
    /// 平均值
    /// </summary>
    public decimal AverageValue { get; set; }

    /// <summary>
    /// 关键指标
    /// </summary>
    public Dictionary<string, object> KeyMetrics { get; set; } = new();
}

/// <summary>
/// 聚合数据项DTO
/// </summary>
public class AggregatedDataItemDto
{
    /// <summary>
    /// 维度标签
    /// </summary>
    public string Label { get; set; } = null!;

    /// <summary>
    /// 维度值
    /// </summary>
    public string Value { get; set; } = null!;

    /// <summary>
    /// 度量指标值
    /// </summary>
    public decimal MetricValue { get; set; }

    /// <summary>
    /// 百分比 (占总数的比例)
    /// </summary>
    public decimal Percentage { get; set; }

    /// <summary>
    /// 额外的维度信息
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 分页元数据DTO
/// </summary>
public class PaginationMetadataDto
{
    public int CurrentPage { get; set; }
    public int PageSize { get; set; }
    public int TotalCount { get; set; }
    public int TotalPages { get; set; }
    public bool HasNext { get; set; }
    public bool HasPrevious { get; set; }
}

/// <summary>
/// 执行元数据DTO
/// </summary>
public class ExecutionMetadataDto
{
    /// <summary>
    /// 查询执行时间 (毫秒)
    /// </summary>
    public long ExecutionTimeMs { get; set; }

    /// <summary>
    /// 数据源信息
    /// </summary>
    public string DataSource { get; set; } = null!;

    /// <summary>
    /// 是否使用了缓存
    /// </summary>
    public bool FromCache { get; set; }

    /// <summary>
    /// 查询维度和度量组合
    /// </summary>
    public string QuerySignature { get; set; } = null!;
}