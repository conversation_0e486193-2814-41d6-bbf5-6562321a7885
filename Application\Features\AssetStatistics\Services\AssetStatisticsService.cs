using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.AssetStatistics.Dtos;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Entities;
using ItAssetsSystem.Models.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ApiResponse = ItAssetsSystem.Application.Common.Dtos.ApiResponse;

namespace ItAssetsSystem.Application.Features.AssetStatistics.Services
{
    /// <summary>
    /// 资产统计服务实现
    /// </summary>
    public class AssetStatisticsService : IAssetStatisticsService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<AssetStatisticsService> _logger;

        public AssetStatisticsService(
            AppDbContext context,
            ILogger<AssetStatisticsService> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<ApiResponse<AssetOverallStatisticsDto>> GetOverallStatisticsAsync(AssetStatisticsQueryDto query)
        {
            try
            {
                var assetsQuery = BuildBaseQuery(query);

                var totalAssets = await assetsQuery.CountAsync();
                var normalAssets = await assetsQuery.CountAsync(a => a.Status == (int)AssetStatus.InUse);
                var idleAssets = await assetsQuery.CountAsync(a => a.Status == (int)AssetStatus.Idle);
                var maintenanceAssets = await assetsQuery.CountAsync(a => a.Status == (int)AssetStatus.UnderMaintenance);
                var faultAssets = await assetsQuery.CountAsync(a => a.Status == (int)AssetStatus.Faulty);
                var scrapAssets = await assetsQuery.CountAsync(a => a.Status == (int)AssetStatus.Scrapped);

                var assetTypeCount = await _context.AssetTypes.CountAsync(at => at.IsActive);
                var locationCount = await _context.Locations.CountAsync(l => l.Type == 0);
                var departmentCount = await _context.Departments.CountAsync();

                var normalRate = totalAssets > 0 ? Math.Round((decimal)normalAssets / totalAssets * 100, 2) : 0;
                var faultRate = totalAssets > 0 ? Math.Round((decimal)faultAssets / totalAssets * 100, 2) : 0;

                var result = new AssetOverallStatisticsDto
                {
                    TotalAssets = totalAssets,
                    NormalAssets = normalAssets,
                    FaultAssets = faultAssets,
                    MaintenanceAssets = maintenanceAssets,
                    IdleAssets = idleAssets,
                    ScrapAssets = scrapAssets,
                    NormalRate = normalRate,
                    FaultRate = faultRate,
                    AssetTypeCount = assetTypeCount,
                    LocationCount = locationCount,
                    DepartmentCount = departmentCount
                };

                return ApiResponse<AssetOverallStatisticsDto>.CreateSuccess(result, "获取资产总体统计成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取资产总体统计时发生错误");
                return ApiResponse<AssetOverallStatisticsDto>.CreateFail("获取资产总体统计失败: " + ex.Message);
            }
        }

        public async Task<ApiResponse<List<AssetTypeStatisticsDto>>> GetStatisticsByTypeAsync(AssetStatisticsQueryDto query)
        {
            try
            {
                var assetsQuery = BuildBaseQuery(query);

                var typeStatistics = await assetsQuery
                    .GroupBy(a => new { a.AssetTypeId, a.AssetType.Name })
                    .Select(g => new AssetTypeStatisticsDto
                    {
                        AssetTypeId = g.Key.AssetTypeId,
                        AssetTypeName = g.Key.Name,
                        AssetCount = g.Count(),
                        NormalCount = g.Count(a => a.Status == (int)AssetStatus.InUse),
                        FaultCount = g.Count(a => a.Status == (int)AssetStatus.Faulty),
                        MaintenanceCount = g.Count(a => a.Status == (int)AssetStatus.UnderMaintenance)
                    })
                    .OrderByDescending(x => x.AssetCount)
                    .ToListAsync();

                var totalAssets = typeStatistics.Sum(x => x.AssetCount);
                foreach (var stat in typeStatistics)
                {
                    stat.NormalRate = stat.AssetCount > 0 ? Math.Round((decimal)stat.NormalCount / stat.AssetCount * 100, 2) : 0;
                    stat.FaultRate = stat.AssetCount > 0 ? Math.Round((decimal)stat.FaultCount / stat.AssetCount * 100, 2) : 0;
                    stat.Percentage = totalAssets > 0 ? Math.Round((decimal)stat.AssetCount / totalAssets * 100, 2) : 0;
                }

                return ApiResponse<List<AssetTypeStatisticsDto>>.CreateSuccess(typeStatistics, "获取按资产类型统计成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取按资产类型统计时发生错误");
                return ApiResponse<List<AssetTypeStatisticsDto>>.CreateFail("获取按资产类型统计失败: " + ex.Message);
            }
        }

        public async Task<ApiResponse<List<AssetRegionStatisticsDto>>> GetStatisticsByRegionAsync(AssetStatisticsQueryDto query)
        {
            try
            {
                // 目标统计维度：type=2（工序级别）
                var targetLocationType = query.LocationType ?? 2;

                _logger.LogInformation($"区域统计查询条件: LocationId={query.LocationId}, DepartmentId={query.DepartmentId}, TargetLocationType={targetLocationType}");

                // 获取所有资产及其位置Path
                var assetsQuery = BuildBaseQuery(query)
                    .Where(a => a.Location != null && !string.IsNullOrEmpty(a.Location.Path));

                var assetsWithPath = await assetsQuery
                    .Select(a => new
                    {
                        a.Id,
                        a.Status,
                        AssetTypeName = a.AssetType.Name,
                        LocationPath = a.Location.Path
                    })
                    .ToListAsync();

                _logger.LogInformation($"获取到 {assetsWithPath.Count} 个有位置路径的资产");

                // 按Path第四个值分组统计
                var assetsByRegion = new Dictionary<int, List<dynamic>>();

                foreach (var asset in assetsWithPath)
                {
                    var pathParts = asset.LocationPath.Split(',');
                    if (pathParts.Length >= 4 && int.TryParse(pathParts[3], out int regionId))
                    {
                        if (!assetsByRegion.ContainsKey(regionId))
                        {
                            assetsByRegion[regionId] = new List<dynamic>();
                        }
                        assetsByRegion[regionId].Add(asset);
                    }
                }

                _logger.LogInformation($"按Path第四个值分组后得到 {assetsByRegion.Count} 个区域组");

                // 获取这些区域ID对应的位置信息
                var regionIds = assetsByRegion.Keys.ToList();
                var regionInfos = await _context.Locations
                    .Where(l => regionIds.Contains(l.Id))
                    .ToDictionaryAsync(l => l.Id, l => l.Name);

                // 构建统计结果
                var regionStatistics = assetsByRegion.Select(kvp =>
                {
                    var regionId = kvp.Key;
                    var assets = kvp.Value;
                    var regionName = regionInfos.ContainsKey(regionId) ? regionInfos[regionId] : $"位置{regionId}";

                    // 计算各种资产类型数量
                    var computers = assets.Count(a => a.AssetTypeName.Contains("电脑") || a.AssetTypeName.Contains("计算机"));
                    var pdas = assets.Count(a => a.AssetTypeName.Contains("PDA") || a.AssetTypeName.Contains("手持"));
                    var others = assets.Count - computers - pdas;

                    return new AssetRegionStatisticsDto
                    {
                        RegionId = regionId,
                        RegionName = regionName,
                        AssetCount = assets.Count,
                        NormalCount = assets.Count(a => a.Status == (int)AssetStatus.InUse),
                        FaultCount = assets.Count(a => a.Status == (int)AssetStatus.Faulty),
                        MaintenanceCount = assets.Count(a => a.Status == (int)AssetStatus.UnderMaintenance)
                    };
                })
                .Where(r => r.AssetCount > 0)
                .OrderByDescending(r => r.AssetCount)
                .ToList();

                _logger.LogInformation($"查询到 {regionStatistics.Count} 个区域的统计数据");

                var totalAssets = regionStatistics.Sum(x => x.AssetCount);
                foreach (var stat in regionStatistics)
                {
                    stat.NormalRate = stat.AssetCount > 0 ? Math.Round((decimal)stat.NormalCount / stat.AssetCount * 100, 2) : 0;
                    stat.FaultRate = stat.AssetCount > 0 ? Math.Round((decimal)stat.FaultCount / stat.AssetCount * 100, 2) : 0;
                    stat.Percentage = totalAssets > 0 ? Math.Round((decimal)stat.AssetCount / totalAssets * 100, 2) : 0;
                }

                return ApiResponse<List<AssetRegionStatisticsDto>>.CreateSuccess(regionStatistics, "获取按区域统计成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取按区域统计时发生错误");
                return ApiResponse<List<AssetRegionStatisticsDto>>.CreateFail("获取按区域统计失败: " + ex.Message);
            }
        }

        public async Task<ApiResponse<List<AssetDepartmentStatisticsDto>>> GetStatisticsByDepartmentAsync(AssetStatisticsQueryDto query)
        {
            try
            {
                _logger.LogInformation($"部门统计查询条件: LocationId={query.LocationId}, DepartmentId={query.DepartmentId}");

                // 获取所有资产及其位置信息
                var assetsQuery = BuildBaseQuery(query)
                    .Where(a => a.Location != null && !string.IsNullOrEmpty(a.Location.Path));

                var assetsWithLocation = await assetsQuery
                    .Select(a => new
                    {
                        a.Id,
                        a.Status,
                        AssetTypeName = a.AssetType.Name,
                        LocationId = a.LocationId.Value,
                        LocationDefaultDepartmentId = a.Location.DefaultDepartmentId,
                        LocationPath = a.Location.Path
                    })
                    .ToListAsync();

                _logger.LogInformation($"获取到 {assetsWithLocation.Count} 个资产的位置信息");

                // 获取所有位置信息（用于查找Path中第二个值对应的位置的部门）
                var allLocations = await _context.Locations
                    .Select(l => new { l.Id, l.DefaultDepartmentId })
                    .ToDictionaryAsync(l => l.Id, l => l.DefaultDepartmentId);

                // 获取所有部门信息
                var allDepartments = await _context.Departments
                    .ToDictionaryAsync(d => d.Id, d => d.Name);

                // 为每个资产确定部门（统一使用Path第二个值对应位置的部门）
                var assetsByDepartment = new Dictionary<int, List<dynamic>>();

                foreach (var asset in assetsWithLocation)
                {
                    int? departmentId = null;

                    // 统一逻辑：使用Path中第二个值对应位置的部门
                    var pathParts = asset.LocationPath.Split(',');
                    if (pathParts.Length >= 2 && int.TryParse(pathParts[1], out int secondLocationId))
                    {
                        if (allLocations.ContainsKey(secondLocationId) && allLocations[secondLocationId].HasValue)
                        {
                            departmentId = allLocations[secondLocationId].Value;
                            _logger.LogDebug($"资产 {asset.Id} 使用Path第二个位置({secondLocationId})的部门: {departmentId}");
                        }
                    }

                    // 如果找到了有效的部门ID，添加到分组中
                    if (departmentId.HasValue && allDepartments.ContainsKey(departmentId.Value))
                    {
                        if (!assetsByDepartment.ContainsKey(departmentId.Value))
                        {
                            assetsByDepartment[departmentId.Value] = new List<dynamic>();
                        }
                        assetsByDepartment[departmentId.Value].Add(asset);
                    }
                }

                _logger.LogInformation($"按部门分组后得到 {assetsByDepartment.Count} 个部门组");

                // 构建统计结果
                var departmentStatistics = assetsByDepartment.Select(kvp =>
                {
                    var departmentId = kvp.Key;
                    var assets = kvp.Value;
                    var departmentName = allDepartments[departmentId];

                    return new AssetDepartmentStatisticsDto
                    {
                        DepartmentId = departmentId,
                        DepartmentName = departmentName,
                        AssetCount = assets.Count,
                        NormalCount = assets.Count(a => a.Status == (int)AssetStatus.InUse),
                        FaultCount = assets.Count(a => a.Status == (int)AssetStatus.Faulty),
                        MaintenanceCount = assets.Count(a => a.Status == (int)AssetStatus.UnderMaintenance)
                    };
                })
                .Where(d => d.AssetCount > 0)
                .OrderByDescending(d => d.AssetCount)
                .ToList();

                // 计算百分比和比率
                var totalAssets = departmentStatistics.Sum(x => x.AssetCount);
                foreach (var stat in departmentStatistics)
                {
                    stat.NormalRate = stat.AssetCount > 0 ? Math.Round((decimal)stat.NormalCount / stat.AssetCount * 100, 2) : 0;
                    stat.FaultRate = stat.AssetCount > 0 ? Math.Round((decimal)stat.FaultCount / stat.AssetCount * 100, 2) : 0;
                    stat.Percentage = totalAssets > 0 ? Math.Round((decimal)stat.AssetCount / totalAssets * 100, 2) : 0;
                }

                _logger.LogInformation($"按部门统计完成，共 {departmentStatistics.Count} 个部门");

                return ApiResponse<List<AssetDepartmentStatisticsDto>>.CreateSuccess(departmentStatistics, "获取按部门统计成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取按部门统计时发生错误");
                return ApiResponse<List<AssetDepartmentStatisticsDto>>.CreateFail("获取按部门统计失败: " + ex.Message);
            }
        }

        public async Task<ApiResponse<List<AssetTrendDataDto>>> GetWeeklyTrendAsync(AssetTrendQueryDto query)
        {
            try
            {
                var endDate = query.EndDate ?? DateTime.Now;
                var startDate = query.StartDate ?? endDate.AddDays(-42); // 6周

                var trendData = await GenerateTrendData(startDate, endDate, "week", query);

                return ApiResponse<List<AssetTrendDataDto>>.CreateSuccess(trendData, "获取周趋势数据成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取周趋势数据时发生错误");
                return ApiResponse<List<AssetTrendDataDto>>.CreateFail("获取周趋势数据失败: " + ex.Message);
            }
        }

        public async Task<ApiResponse<List<AssetTrendDataDto>>> GetDailyTrendAsync(AssetTrendQueryDto query)
        {
            try
            {
                var endDate = query.EndDate ?? DateTime.Now;
                var startDate = query.StartDate ?? endDate.AddDays(-30); // 30天

                var trendData = await GenerateTrendData(startDate, endDate, "day", query);

                return ApiResponse<List<AssetTrendDataDto>>.CreateSuccess(trendData, "获取日趋势数据成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取日趋势数据时发生错误");
                return ApiResponse<List<AssetTrendDataDto>>.CreateFail("获取日趋势数据失败: " + ex.Message);
            }
        }

        public async Task<ApiResponse<List<AssetTrendDataDto>>> GetMonthlyTrendAsync(AssetTrendQueryDto query)
        {
            try
            {
                var endDate = query.EndDate ?? DateTime.Now;
                var startDate = query.StartDate ?? endDate.AddMonths(-12); // 12个月

                var trendData = await GenerateTrendData(startDate, endDate, "month", query);

                return ApiResponse<List<AssetTrendDataDto>>.CreateSuccess(trendData, "获取月趋势数据成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取月趋势数据时发生错误");
                return ApiResponse<List<AssetTrendDataDto>>.CreateFail("获取月趋势数据失败: " + ex.Message);
            }
        }

        public async Task<ApiResponse<AssetCombinedStatisticsDto>> GetCombinedStatisticsAsync(AssetStatisticsQueryDto query)
        {
            try
            {
                var assetsQuery = BuildBaseQuery(query);

                // 区域和类型交叉统计
                var regionTypeStats = await assetsQuery
                    .Where(a => a.Location != null && a.Location.Type == 0)
                    .GroupBy(a => new { 
                        a.LocationId, 
                        LocationName = a.Location.Name, 
                        a.AssetTypeId, 
                        AssetTypeName = a.AssetType.Name 
                    })
                    .Select(g => new RegionTypeStatisticsDto
                    {
                        RegionId = g.Key.LocationId.Value,
                        RegionName = g.Key.LocationName,
                        AssetTypeId = g.Key.AssetTypeId,
                        AssetTypeName = g.Key.AssetTypeName,
                        AssetCount = g.Count(),
                        NormalCount = g.Count(a => a.Status == (int)AssetStatus.InUse),
                        FaultCount = g.Count(a => a.Status == (int)AssetStatus.Faulty)
                    })
                    .ToListAsync();

                // 部门和类型交叉统计
                var departmentTypeStats = await assetsQuery
                    .Where(a => a.Department != null)
                    .GroupBy(a => new { 
                        a.DepartmentId, 
                        DepartmentName = a.Department.Name, 
                        a.AssetTypeId, 
                        AssetTypeName = a.AssetType.Name 
                    })
                    .Select(g => new DepartmentTypeStatisticsDto
                    {
                        DepartmentId = g.Key.DepartmentId.Value,
                        DepartmentName = g.Key.DepartmentName,
                        AssetTypeId = g.Key.AssetTypeId,
                        AssetTypeName = g.Key.AssetTypeName,
                        AssetCount = g.Count(),
                        NormalCount = g.Count(a => a.Status == (int)AssetStatus.InUse),
                        FaultCount = g.Count(a => a.Status == (int)AssetStatus.Faulty)
                    })
                    .ToListAsync();

                // 状态分布
                var statusDistribution = new StatusDistributionDto
                {
                    NormalCount = await assetsQuery.CountAsync(a => a.Status == (int)AssetStatus.InUse),
                    FaultCount = await assetsQuery.CountAsync(a => a.Status == (int)AssetStatus.Faulty),
                    MaintenanceCount = await assetsQuery.CountAsync(a => a.Status == (int)AssetStatus.UnderMaintenance),
                    PendingCount = await assetsQuery.CountAsync(a => a.Status == (int)AssetStatus.Idle),
                    ScrapCount = await assetsQuery.CountAsync(a => a.Status == (int)AssetStatus.Scrapped)
                };

                var result = new AssetCombinedStatisticsDto
                {
                    RegionTypeStatistics = regionTypeStats,
                    DepartmentTypeStatistics = departmentTypeStats,
                    StatusDistribution = statusDistribution
                };

                return ApiResponse<AssetCombinedStatisticsDto>.CreateSuccess(result, "获取组合统计数据成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取组合统计数据时发生错误");
                return ApiResponse<AssetCombinedStatisticsDto>.CreateFail("获取组合统计数据失败: " + ex.Message);
            }
        }

        public async Task<ApiResponse<List<AssetTypeDto>>> GetAssetTypesAsync()
        {
            try
            {
                var assetTypes = await _context.AssetTypes
                    .Where(at => at.IsActive)
                    .Select(at => new AssetTypeDto
                    {
                        AssetTypeId = at.Id,
                        AssetTypeName = at.Name,
                        Description = at.Description,
                        IsActive = at.IsActive,
                        AssetCount = _context.Assets.Count(a => a.AssetTypeId == at.Id)
                    })
                    .OrderBy(at => at.AssetTypeName)
                    .ToListAsync();

                return ApiResponse<List<AssetTypeDto>>.CreateSuccess(assetTypes, "获取资产类型列表成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取资产类型列表时发生错误");
                return ApiResponse<List<AssetTypeDto>>.CreateFail("获取资产类型列表失败: " + ex.Message);
            }
        }

        public async Task<ApiResponse<List<AssetRegionDto>>> GetRegionsAsync()
        {
            try
            {
                var regions = await _context.Locations
                    .Where(l => l.Type == 0)
                    .Select(l => new AssetRegionDto
                    {
                        RegionId = l.Id,
                        RegionName = l.Name,
                        Description = l.Description,
                        IsActive = true,
                        AssetCount = _context.Assets.Count(a => a.LocationId == l.Id)
                    })
                    .OrderBy(r => r.RegionName)
                    .ToListAsync();

                return ApiResponse<List<AssetRegionDto>>.CreateSuccess(regions, "获取区域列表成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取区域列表时发生错误");
                return ApiResponse<List<AssetRegionDto>>.CreateFail("获取区域列表失败: " + ex.Message);
            }
        }

        public async Task<ApiResponse<List<AssetDepartmentDto>>> GetDepartmentsAsync()
        {
            try
            {
                var departments = await _context.Departments
                    .Select(d => new AssetDepartmentDto
                    {
                        DepartmentId = d.Id,
                        DepartmentName = d.Name,
                        Description = d.Description,
                        IsActive = true,
                        AssetCount = _context.Assets.Count(a => a.DepartmentId == d.Id)
                    })
                    .OrderBy(d => d.DepartmentName)
                    .ToListAsync();

                return ApiResponse<List<AssetDepartmentDto>>.CreateSuccess(departments, "获取部门列表成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取部门列表时发生错误");
                return ApiResponse<List<AssetDepartmentDto>>.CreateFail("获取部门列表失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 根据位置类型查找父级位置
        /// </summary>
        /// <param name="locationId">当前位置ID</param>
        /// <param name="targetType">目标位置类型</param>
        /// <param name="locationDict">位置字典</param>
        /// <returns>目标类型的父级位置ID</returns>
        private int? FindParentLocationByType(int locationId, int targetType, Dictionary<int, object> locationDict)
        {
            if (!locationDict.ContainsKey(locationId))
            {
                _logger.LogWarning($"位置ID {locationId} 在字典中不存在");
                return null;
            }

            dynamic currentLocation = locationDict[locationId];

            _logger.LogDebug($"检查位置: ID={locationId}, Type={currentLocation.Type}, Name={currentLocation.Name}");

            // 如果当前位置就是目标类型，直接返回
            if (currentLocation.Type == targetType)
            {
                _logger.LogDebug($"找到目标类型位置: ID={locationId}, Name={currentLocation.Name}");
                return locationId;
            }

            // 如果当前位置类型小于目标类型，说明已经超过了，返回null
            if (currentLocation.Type < targetType)
            {
                _logger.LogDebug($"位置类型 {currentLocation.Type} 小于目标类型 {targetType}，停止查找");
                return null;
            }

            // 向上查找父级位置
            if (currentLocation.ParentId != null && currentLocation.ParentId > 0)
            {
                _logger.LogDebug($"向上查找父级位置: ParentId={currentLocation.ParentId}");
                return FindParentLocationByType((int)currentLocation.ParentId, targetType, locationDict);
            }

            _logger.LogDebug($"位置 {locationId} 没有父级位置");
            return null;
        }

        /// <summary>
        /// 构建基础查询
        /// </summary>
        private IQueryable<Asset> BuildBaseQuery(AssetStatisticsQueryDto query)
        {
            var assetsQuery = _context.Assets
                .Include(a => a.AssetType)
                .Include(a => a.Location)
                .Include(a => a.Department)
                .AsNoTracking();

            if (query.StartDate.HasValue)
            {
                assetsQuery = assetsQuery.Where(a => a.CreatedAt >= query.StartDate.Value);
            }

            if (query.EndDate.HasValue)
            {
                assetsQuery = assetsQuery.Where(a => a.CreatedAt <= query.EndDate.Value);
            }

            if (query.AssetTypeId.HasValue)
            {
                assetsQuery = assetsQuery.Where(a => a.AssetTypeId == query.AssetTypeId.Value);
            }

            if (query.LocationId.HasValue)
            {
                // 使用统一的层级化筛选逻辑，包含所有子位置
                var locationIds = await GetChildIdsAsync(query.LocationId.Value, "location");
                assetsQuery = assetsQuery.Where(a => locationIds.Contains(a.LocationId ?? 0));
                _logger.LogInformation($"位置筛选: 目标位置ID={query.LocationId.Value}, 相关位置数={locationIds.Count}");
            }

            if (query.DepartmentId.HasValue)
            {
                // 使用统一的层级化筛选逻辑，包含所有子位置
                var locationIds = await GetChildIdsAsync(query.DepartmentId.Value, "department");
                assetsQuery = assetsQuery.Where(a => locationIds.Contains(a.LocationId ?? 0));
                _logger.LogInformation($"部门筛选: 目标部门ID={query.DepartmentId.Value}, 相关位置数={locationIds.Count}");
            }

            if (!string.IsNullOrEmpty(query.Status))
            {
                if (int.TryParse(query.Status, out int statusValue))
                {
                    assetsQuery = assetsQuery.Where(a => a.Status == statusValue);
                }
            }

            if (!string.IsNullOrEmpty(query.Keyword))
            {
                assetsQuery = assetsQuery.Where(a => 
                    a.Name.Contains(query.Keyword) ||
                    a.AssetCode.Contains(query.Keyword) ||
                    a.SerialNumber.Contains(query.Keyword));
            }

            return assetsQuery;
        }

        /// <summary>
        /// 生成趋势数据
        /// </summary>
        private async Task<List<AssetTrendDataDto>> GenerateTrendData(
            DateTime startDate, DateTime endDate, string period, AssetTrendQueryDto query)
        {
            var trendData = new List<AssetTrendDataDto>();
            var current = startDate;

            while (current <= endDate)
            {
                DateTime periodStart, periodEnd;
                string dateLabel;

                switch (period)
                {
                    case "day":
                        periodStart = current.Date;
                        periodEnd = current.Date.AddDays(1).AddMilliseconds(-1);
                        dateLabel = current.ToString("MM-dd");
                        current = current.AddDays(1);
                        break;
                    case "week":
                        var startOfWeek = current.AddDays(-(int)current.DayOfWeek);
                        periodStart = startOfWeek.Date;
                        periodEnd = startOfWeek.AddDays(7).AddMilliseconds(-1);
                        dateLabel = $"W{CultureInfo.CurrentCulture.Calendar.GetWeekOfYear(current, CalendarWeekRule.FirstDay, DayOfWeek.Sunday)}";
                        current = current.AddDays(7);
                        break;
                    case "month":
                        periodStart = new DateTime(current.Year, current.Month, 1);
                        periodEnd = periodStart.AddMonths(1).AddMilliseconds(-1);
                        dateLabel = current.ToString("yyyy-MM");
                        current = current.AddMonths(1);
                        break;
                    default:
                        throw new ArgumentException("不支持的周期类型");
                }

                var assetsQuery = _context.Assets
                    .Include(a => a.AssetType)
                    .Include(a => a.Location)
                    .Include(a => a.Department)
                    .Where(a => a.CreatedAt <= periodEnd)
                    .AsNoTracking();

                // 应用过滤条件
                if (query.AssetTypeId.HasValue)
                {
                    assetsQuery = assetsQuery.Where(a => a.AssetTypeId == query.AssetTypeId.Value);
                }

                if (query.RegionId.HasValue)
                {
                    // 使用统一的层级化筛选逻辑，包含所有子位置
                    var locationIds = await GetChildIdsAsync(query.RegionId.Value, "location");
                    assetsQuery = assetsQuery.Where(a => locationIds.Contains(a.LocationId ?? 0));
                }

                if (query.DepartmentId.HasValue)
                {
                    // 使用统一的层级化筛选逻辑，包含所有子位置
                    var locationIds = await GetChildIdsAsync(query.DepartmentId.Value, "department");
                    assetsQuery = assetsQuery.Where(a => locationIds.Contains(a.LocationId ?? 0));
                }

                var totalAssets = await assetsQuery.CountAsync();
                var normalAssets = await assetsQuery.CountAsync(a => a.Status == (int)AssetStatus.InUse);
                var faultAssets = await assetsQuery.CountAsync(a => a.Status == (int)AssetStatus.Faulty);
                var maintenanceAssets = await assetsQuery.CountAsync(a => a.Status == (int)AssetStatus.UnderMaintenance);

                var normalRate = totalAssets > 0 ? Math.Round((decimal)normalAssets / totalAssets * 100, 2) : 0;
                var faultRate = totalAssets > 0 ? Math.Round((decimal)faultAssets / totalAssets * 100, 2) : 0;

                // 计算新增和处理的资产
                var newAssets = await assetsQuery
                    .CountAsync(a => a.CreatedAt >= periodStart && a.CreatedAt <= periodEnd);

                var processedAssets = await _context.FaultRecords
                    .CountAsync(fr => fr.CreatedAt >= periodStart && fr.CreatedAt <= periodEnd &&
                               fr.Status == 2); // 2表示已解决

                trendData.Add(new AssetTrendDataDto
                {
                    Date = periodStart,
                    DateLabel = dateLabel,
                    TotalAssets = totalAssets,
                    NormalAssets = normalAssets,
                    FaultAssets = faultAssets,
                    MaintenanceAssets = maintenanceAssets,
                    NormalRate = normalRate,
                    FaultRate = faultRate,
                    NewAssets = newAssets,
                    ProcessedAssets = processedAssets
                });
            }

            return trendData;
        }

        /// <summary>
        /// 查找位置的继承部门ID
        /// 如果当前位置没有部门ID，向上查找父级位置，直到找到部门ID或到达产线(Type=1)
        /// </summary>
        /// <param name="locationId">位置ID</param>
        /// <param name="locationDict">位置字典</param>
        /// <returns>继承的部门ID</returns>
        private int? FindInheritedDepartmentId(int locationId, Dictionary<int, object> locationDict)
        {
            if (!locationDict.ContainsKey(locationId))
                return null;

            dynamic currentLocation = locationDict[locationId];
            var maxDepth = 10; // 防止无限循环
            var depth = 0;

            while (depth < maxDepth)
            {
                // 如果当前位置有部门ID，直接返回
                if (currentLocation.DefaultDepartmentId != null)
                {
                    return (int)currentLocation.DefaultDepartmentId;
                }

                // 如果已经到达产线级别(Type=1)但没有部门ID，说明数据有问题
                if ((int)currentLocation.Type == 1)
                {
                    _logger.LogWarning($"产线位置 {locationId} (Type=1) 没有设置DefaultDepartmentId");
                    return null;
                }

                // 向上查找父级位置
                if (currentLocation.ParentId == null)
                {
                    _logger.LogWarning($"位置 {locationId} 没有父级位置且没有部门ID");
                    return null;
                }

                int parentId = (int)currentLocation.ParentId;
                if (!locationDict.ContainsKey(parentId))
                {
                    _logger.LogWarning($"位置 {locationId} 的父级位置 {parentId} 不存在");
                    return null;
                }

                currentLocation = locationDict[parentId];
                depth++;
            }

            _logger.LogWarning($"位置 {locationId} 查找部门ID超过最大深度 {maxDepth}");
            return null;
        }

        public async Task<ApiResponse<List<AssetRegionDto>>> GetRegionOptionsAsync()
        {
            try
            {
                _logger.LogInformation("开始获取区域筛选选项（包含子位置资产统计）");

                // 1. 获取基础位置信息（Type=2）
                var type2Locations = await _context.Locations
                    .Where(l => l.Type == 2 && l.IsActive)
                    .ToListAsync();

                _logger.LogInformation($"获取到 {type2Locations.Count} 个type=2的位置");

                // 2. 为每个位置计算资产数量（包含子位置）
                var regionOptions = new List<AssetRegionDto>();
                foreach (var location in type2Locations)
                {
                    // 使用统一的层级化筛选逻辑获取位置ID列表（当前位置+所有子位置）
                    var locationIds = await GetChildIdsAsync(location.Id, "location");

                    // 统计资产数量
                    var assetCount = await _context.Assets
                        .CountAsync(a => locationIds.Contains(a.LocationId ?? 0));

                    regionOptions.Add(new AssetRegionDto
                    {
                        RegionId = location.Id,
                        RegionName = location.Name,
                        Description = location.Description,
                        IsActive = location.IsActive,
                        AssetCount = assetCount
                    });

                    _logger.LogDebug($"位置 {location.Name}(ID:{location.Id}) 包含子位置数: {locationIds.Count - 1}, 资产总数: {assetCount}");
                }

                _logger.LogInformation($"返回 {regionOptions.Count} 个区域选项，总资产数: {regionOptions.Sum(r => r.AssetCount)}");
                return ApiResponse<List<AssetRegionDto>>.CreateSuccess(regionOptions, "获取区域选项成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取区域选项失败");
                return ApiResponse<List<AssetRegionDto>>.CreateFail("获取区域选项失败: " + ex.Message);
            }
        }

        public async Task<ApiResponse<List<AssetDepartmentDto>>> GetDepartmentOptionsAsync()
        {
            try
            {
                _logger.LogInformation("开始获取部门筛选选项（包含子位置资产统计）");

                // 1. 获取所有type=1位置的DefaultDepartmentId
                var type1LocationDepartments = await _context.Locations
                    .Where(l => l.Type == 1 && l.DefaultDepartmentId.HasValue)
                    .Select(l => l.DefaultDepartmentId.Value)
                    .Distinct()
                    .ToListAsync();

                _logger.LogInformation($"从type=1位置获取到 {type1LocationDepartments.Count} 个部门ID");

                // 2. 获取这些部门的详细信息
                var departments = await _context.Departments
                    .Where(d => type1LocationDepartments.Contains(d.Id))
                    .ToListAsync();

                // 3. 为每个部门计算资产数量（包含子位置）
                var departmentOptions = new List<AssetDepartmentDto>();
                foreach (var department in departments)
                {
                    // 使用统一的层级化筛选逻辑获取位置ID列表
                    var locationIds = await GetChildIdsAsync(department.Id, "department");

                    // 统计资产数量
                    var assetCount = await _context.Assets
                        .CountAsync(a => locationIds.Contains(a.LocationId ?? 0));

                    departmentOptions.Add(new AssetDepartmentDto
                    {
                        DepartmentId = department.Id,
                        DepartmentName = department.Name,
                        Description = department.Description,
                        IsActive = true,
                        AssetCount = assetCount
                    });

                    _logger.LogDebug($"部门 {department.Name}(ID:{department.Id}) 关联位置数: {locationIds.Count}, 资产总数: {assetCount}");
                }

                // 按部门名称排序
                departmentOptions = departmentOptions.OrderBy(d => d.DepartmentName).ToList();

                _logger.LogInformation($"返回 {departmentOptions.Count} 个部门选项，总资产数: {departmentOptions.Sum(d => d.AssetCount)}");
                return ApiResponse<List<AssetDepartmentDto>>.CreateSuccess(departmentOptions, "获取部门选项成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取部门选项失败");
                return ApiResponse<List<AssetDepartmentDto>>.CreateFail("获取部门选项失败: " + ex.Message);
            }
        }

        public async Task<ApiResponse<AssetAnalyticsWorkbenchDto>> GetAnalyticsWorkbenchDataAsync()
        {
            try
            {
                _logger.LogInformation("开始获取资产分析工作台完整数据");

                // 创建空查询参数，获取全量数据
                var emptyQuery = new AssetStatisticsQueryDto();

                // 顺序获取各种统计数据（避免DbContext并发问题）
                var kpiData = await GetKpiDataAsync();
                var typeStats = await GetStatisticsByTypeAsync(emptyQuery);
                var regionStats = await GetStatisticsByRegionAsync(emptyQuery);
                var departmentStats = await GetStatisticsByDepartmentAsync(emptyQuery);
                var timeSeriesData = await GetTimeSeriesDataAsync();
                var valueDistribution = await GetValueDistributionDataAsync();
                var matrixData = await GetMatrixDataAsync();
                var statusDistribution = await GetStatusDistributionDataAsync();
                var filterOptions = await GetFilterOptionsDataAsync();

                var result = new AssetAnalyticsWorkbenchDto
                {
                    KpiData = kpiData,
                    TypeStatistics = typeStats.Data ?? new List<AssetTypeStatisticsDto>(),
                    RegionStatistics = regionStats.Data ?? new List<AssetRegionStatisticsDto>(),
                    DepartmentStatistics = departmentStats.Data ?? new List<AssetDepartmentStatisticsDto>(),
                    TimeSeriesData = timeSeriesData,
                    ValueDistribution = valueDistribution,
                    MatrixData = matrixData,
                    StatusDistribution = statusDistribution,
                    FilterOptions = filterOptions,
                    UpdateTime = DateTime.Now
                };

                _logger.LogInformation("资产分析工作台数据获取完成");
                return ApiResponse<AssetAnalyticsWorkbenchDto>.CreateSuccess(result, "获取资产分析工作台数据成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取资产分析工作台数据时发生错误");
                return ApiResponse<AssetAnalyticsWorkbenchDto>.CreateFail("获取资产分析工作台数据失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 获取KPI指标数据
        /// </summary>
        private async Task<AssetKpiDataDto> GetKpiDataAsync()
        {
            var totalAssets = await _context.Assets.CountAsync();
            var totalValue = await _context.Assets.SumAsync(a => a.Price ?? 0);
            var onlineAssets = await _context.Assets.CountAsync(a => a.Status == (int)AssetStatus.InUse);
            var faultAssets = await _context.Assets.CountAsync(a => a.Status == (int)AssetStatus.Faulty);

            var onlineRate = totalAssets > 0 ? Math.Round((double)onlineAssets * 100.0 / totalAssets, 2) : 0;

            // 计算趋势（与上月比较）
            var lastMonth = DateTime.Now.AddMonths(-1);
            var lastMonthAssets = await _context.Assets.CountAsync(a => a.CreatedAt <= lastMonth);
            var lastMonthValue = await _context.Assets.Where(a => a.CreatedAt <= lastMonth).SumAsync(a => a.Price ?? 0);
            var lastMonthOnline = await _context.Assets.CountAsync(a => a.CreatedAt <= lastMonth && a.Status == (int)AssetStatus.InUse);
            var lastMonthFault = await _context.Assets.CountAsync(a => a.CreatedAt <= lastMonth && a.Status == (int)AssetStatus.Faulty);

            var lastMonthOnlineRate = lastMonthAssets > 0 ? (double)lastMonthOnline * 100.0 / lastMonthAssets : 0;

            return new AssetKpiDataDto
            {
                TotalAssets = totalAssets,
                TotalValue = Math.Round(totalValue / 10000, 2), // 转换为万元
                OnlineRate = onlineRate,
                FaultCount = faultAssets,
                TotalTrend = CalculateTrend(totalAssets, lastMonthAssets),
                ValueTrend = CalculateTrend((double)totalValue, (double)lastMonthValue),
                OnlineRateTrend = onlineRate - lastMonthOnlineRate,
                FaultTrend = CalculateTrend(faultAssets, lastMonthFault)
            };
        }

        /// <summary>
        /// 获取时间序列数据
        /// </summary>
        private async Task<AssetTimeSeriesDataDto> GetTimeSeriesDataAsync()
        {
            var timeLabels = new List<string>();
            var onlineData = new List<int>();
            var offlineData = new List<int>();
            var maintenanceData = new List<int>();

            var totalAssets = await _context.Assets.CountAsync();
            var onlineAssets = await _context.Assets.CountAsync(a => a.Status == (int)AssetStatus.InUse);
            var offlineAssets = await _context.Assets.CountAsync(a => a.Status == (int)AssetStatus.Idle);
            var maintenanceAssets = await _context.Assets.CountAsync(a => a.Status == (int)AssetStatus.UnderMaintenance);

            // 生成6个时间点的数据
            for (int i = 0; i < 6; i++)
            {
                var hour = i * 4;
                timeLabels.Add($"{hour:D2}:00");

                // 模拟一天中的变化（工作时间在线率更高）
                var factor = hour >= 8 && hour <= 18 ? 1.0 : 0.8;
                onlineData.Add((int)(onlineAssets * factor));
                offlineData.Add((int)(offlineAssets * (2.0 - factor)));
                maintenanceData.Add(maintenanceAssets);
            }

            return new AssetTimeSeriesDataDto
            {
                TimeLabels = timeLabels,
                OnlineData = onlineData,
                OfflineData = offlineData,
                MaintenanceData = maintenanceData
            };
        }

        /// <summary>
        /// 获取资产价值分布数据
        /// </summary>
        private async Task<AssetValueDistributionDto> GetValueDistributionDataAsync()
        {
            var valueRanges = new List<string> { "0-1万", "1-5万", "5-10万", "10-50万", "50万以上" };
            var assetCounts = new List<int>();
            var totalValues = new List<decimal>();

            // 按价值区间统计
            assetCounts.Add(await _context.Assets.CountAsync(a => (a.Price ?? 0) < 10000));
            assetCounts.Add(await _context.Assets.CountAsync(a => (a.Price ?? 0) >= 10000 && (a.Price ?? 0) < 50000));
            assetCounts.Add(await _context.Assets.CountAsync(a => (a.Price ?? 0) >= 50000 && (a.Price ?? 0) < 100000));
            assetCounts.Add(await _context.Assets.CountAsync(a => (a.Price ?? 0) >= 100000 && (a.Price ?? 0) < 500000));
            assetCounts.Add(await _context.Assets.CountAsync(a => (a.Price ?? 0) >= 500000));

            totalValues.Add(await _context.Assets.Where(a => (a.Price ?? 0) < 10000).SumAsync(a => a.Price ?? 0));
            totalValues.Add(await _context.Assets.Where(a => (a.Price ?? 0) >= 10000 && (a.Price ?? 0) < 50000).SumAsync(a => a.Price ?? 0));
            totalValues.Add(await _context.Assets.Where(a => (a.Price ?? 0) >= 50000 && (a.Price ?? 0) < 100000).SumAsync(a => a.Price ?? 0));
            totalValues.Add(await _context.Assets.Where(a => (a.Price ?? 0) >= 100000 && (a.Price ?? 0) < 500000).SumAsync(a => a.Price ?? 0));
            totalValues.Add(await _context.Assets.Where(a => (a.Price ?? 0) >= 500000).SumAsync(a => a.Price ?? 0));

            return new AssetValueDistributionDto
            {
                ValueRanges = valueRanges,
                AssetCounts = assetCounts,
                TotalValues = totalValues
            };
        }

        /// <summary>
        /// 获取部门资产矩阵数据
        /// </summary>
        private async Task<AssetMatrixDataDto> GetMatrixDataAsync()
        {
            // 获取前5个部门和前5个资产类型
            var topDepartments = await _context.Departments
                .OrderByDescending(d => _context.Assets.Count(a => a.DepartmentId == d.Id))
                .Take(5)
                .Select(d => d.Name)
                .ToListAsync();

            var topAssetTypes = await _context.AssetTypes
                .OrderByDescending(at => _context.Assets.Count(a => a.AssetTypeId == at.Id))
                .Take(5)
                .Select(at => at.Name)
                .ToListAsync();

            var matrixData = new List<List<int>>();

            // 为每个部门生成与资产类型的交叉数据
            foreach (var dept in topDepartments)
            {
                var row = new List<int>();
                foreach (var assetType in topAssetTypes)
                {
                    var count = await _context.Assets
                        .CountAsync(a => a.Department.Name == dept && a.AssetType.Name == assetType);
                    row.Add(count);
                }
                matrixData.Add(row);
            }

            return new AssetMatrixDataDto
            {
                Departments = topDepartments,
                AssetTypes = topAssetTypes,
                MatrixData = matrixData
            };
        }

        /// <summary>
        /// 获取资产状态分布数据
        /// </summary>
        private async Task<AssetStatusDistributionDto> GetStatusDistributionDataAsync()
        {
            var statusLabels = new List<string> { "在用", "闲置", "维护中", "故障", "报废" };
            var statusCounts = new List<int>();

            statusCounts.Add(await _context.Assets.CountAsync(a => a.Status == (int)AssetStatus.InUse));
            statusCounts.Add(await _context.Assets.CountAsync(a => a.Status == (int)AssetStatus.Idle));
            statusCounts.Add(await _context.Assets.CountAsync(a => a.Status == (int)AssetStatus.UnderMaintenance));
            statusCounts.Add(await _context.Assets.CountAsync(a => a.Status == (int)AssetStatus.Faulty));
            statusCounts.Add(await _context.Assets.CountAsync(a => a.Status == (int)AssetStatus.Scrapped));

            var total = statusCounts.Sum();
            var statusPercentages = statusCounts.Select(count =>
                total > 0 ? Math.Round((double)count * 100.0 / total, 2) : 0).ToList();

            return new AssetStatusDistributionDto
            {
                StatusLabels = statusLabels,
                StatusCounts = statusCounts,
                StatusPercentages = statusPercentages
            };
        }

        /// <summary>
        /// 获取筛选选项数据
        /// </summary>
        private async Task<AssetFilterOptionsDto> GetFilterOptionsDataAsync()
        {
            var assetTypes = await GetAssetTypesAsync();
            var regions = await GetRegionOptionsAsync();
            var departments = await GetDepartmentOptionsAsync();

            var statuses = new List<AssetStatusOptionDto>
            {
                new() { Value = (int)AssetStatus.InUse, Label = "在用", Color = "#10b981" },
                new() { Value = (int)AssetStatus.Idle, Label = "闲置", Color = "#f59e0b" },
                new() { Value = (int)AssetStatus.UnderMaintenance, Label = "维护中", Color = "#3b82f6" },
                new() { Value = (int)AssetStatus.Faulty, Label = "故障", Color = "#ef4444" },
                new() { Value = (int)AssetStatus.Scrapped, Label = "报废", Color = "#6b7280" }
            };

            return new AssetFilterOptionsDto
            {
                AssetTypes = assetTypes.Data ?? new List<AssetTypeDto>(),
                Regions = regions.Data ?? new List<AssetRegionDto>(),
                Departments = departments.Data ?? new List<AssetDepartmentDto>(),
                Statuses = statuses
            };
        }



        /// <summary>
        /// 获取指定父级ID的所有子级ID（包括自身）
        /// </summary>
        /// <param name="parentId">父级ID</param>
        /// <param name="entityType">实体类型：location 或 department</param>
        /// <returns>包含父级和所有子级的ID列表</returns>
        private async Task<List<int>> GetChildIdsAsync(int parentId, string entityType)
        {
            if (entityType == "location")
            {
                // 位置层级筛选
                var allIds = new List<int> { parentId };
                var childLocations = await _context.Locations
                    .Where(l => l.Path.Contains($"/{parentId}/") || l.Path.EndsWith($"/{parentId}"))
                    .Select(l => l.Id)
                    .ToListAsync();
                allIds.AddRange(childLocations);

                _logger.LogInformation($"位置层级筛选: 父级ID={parentId}, 包含子级数={childLocations.Count}, 总数={allIds.Count}");
                return allIds;
            }
            else if (entityType == "department")
            {
                // 部门层级筛选（通过位置的DefaultDepartmentId关联）
                var relevantLocationIds = await _context.Locations
                    .Where(l => l.DefaultDepartmentId == parentId)
                    .Select(l => l.Id)
                    .ToListAsync();

                var allLocationIds = new List<int>(relevantLocationIds);
                foreach (var locationId in relevantLocationIds)
                {
                    var childLocationIds = await _context.Locations
                        .Where(l => l.Path.Contains($"/{locationId}/") || l.Path.EndsWith($"/{locationId}"))
                        .Select(l => l.Id)
                        .ToListAsync();
                    allLocationIds.AddRange(childLocationIds);
                }

                _logger.LogInformation($"部门层级筛选: 部门ID={parentId}, 直接关联位置数={relevantLocationIds.Count}, 总位置数={allLocationIds.Count}");
                return allLocationIds;
            }

            return new List<int> { parentId };
        }

        /// <summary>
        /// 计算趋势百分比
        /// </summary>
        private double CalculateTrend(double current, double previous)
        {
            if (previous == 0) return current > 0 ? 100 : 0;
            return Math.Round(((current - previous) / previous) * 100, 2);
        }
    }
}