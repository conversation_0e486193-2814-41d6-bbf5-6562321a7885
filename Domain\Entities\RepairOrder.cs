// File: Domain/Entities/RepairOrder.cs
// Description: 返厂维修单主表实体，支持主表-明细表设计模式

#nullable enable
using System;
using System.Collections.Generic;
using ItAssetsSystem.Models.Entities;

namespace ItAssetsSystem.Domain.Entities;

public class RepairOrder
{
    public int Id { get; set; }
    public string OrderCode { get; set; } = null!;
    public int SupplierId { get; set; }
    public DateTime? SendDate { get; set; }
    public DateTime? ExpectedReturnDate { get; set; }
    public DateTime? ActualReturnDate { get; set; }
    public decimal TotalCost { get; set; }
    public int Status { get; set; }
    public string? Notes { get; set; }
    public int CreatorId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }

    public virtual Supplier Supplier { get; set; } = null!;
    public virtual User Creator { get; set; } = null!;
    public virtual ICollection<RepairItem> RepairItems { get; set; } = new List<RepairItem>();
}