// File: frontend/src/views/tasks/components/NotificationCenter.vue
// Description: 任务通知中心组件，显示任务相关通知信息

<template>
  <div class="notification-center">
    <!-- 作为drawer使用时 -->
    <el-drawer
      v-if="isDrawerMode"
      :model-value="visible"
      @update:model-value="$emit('update:visible', $event)"
      title="通知中心"
      direction="rtl"
      size="400px"
    >
      <div class="notification-drawer-content">
        <div class="notification-header">
          <div class="header-actions">
            <el-button
              v-if="hasUnread"
              link
              type="primary"
              @click="markAllAsRead"
            >
              全部标为已读
            </el-button>
            <el-button
              link
              type="warning"
              @click="testRealTimeNotification"
            >
              测试实时通知
            </el-button>
          </div>
        </div>
        
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="全部" name="all">
            <NotificationList
              :notifications="notifications"
              @read="markAsRead"
              @click="handleNotificationClick"
            />
          </el-tab-pane>
          <el-tab-pane label="任务" name="task">
            <NotificationList
              :notifications="taskNotifications"
              @read="markAsRead"
              @click="handleNotificationClick"
            />
          </el-tab-pane>
          <el-tab-pane label="系统" name="system">
            <NotificationList
              :notifications="systemNotifications"
              @read="markAsRead"
              @click="handleNotificationClick"
            />
          </el-tab-pane>
        </el-tabs>
        
        <div class="notification-footer">
          <el-button
            type="primary"
            @click="viewAllNotifications"
            style="cursor: pointer !important;"
          >
            查看所有通知
          </el-button>
        </div>
      </div>
    </el-drawer>
    
    <!-- 作为dropdown使用时 -->
    <el-dropdown v-else trigger="click" @visible-change="onVisibleChange">
      <span class="notification-trigger">
        <el-badge :value="unreadCount" :hidden="unreadCount === 0" class="notification-badge">
          <el-button class="notification-icon-button" :icon="Bell" circle type="primary" text />
        </el-badge>
      </span>
      
      <template #dropdown>
        <div class="notification-dropdown-content">
          <div class="notification-header">
            <h3>通知中心</h3>
            <div class="header-actions">
              <el-button 
                v-if="hasUnread" 
                link 
                type="primary" 
                @click="markAllAsRead"
              >
                全部标为已读
              </el-button>

            </div>
          </div>
          
          <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <el-tab-pane label="全部" name="all">
              <NotificationList
                :notifications="notifications"
                @read="markAsRead"
                @click="handleNotificationClick"
              />
            </el-tab-pane>
            <el-tab-pane label="任务" name="task">
              <NotificationList
                :notifications="taskNotifications"
                @read="markAsRead"
                @click="handleNotificationClick"
              />
            </el-tab-pane>
            <el-tab-pane label="系统" name="system">
              <NotificationList
                :notifications="systemNotifications"
                @read="markAsRead"
                @click="handleNotificationClick"
              />
            </el-tab-pane>
          </el-tabs>
          
          <div class="notification-footer">
            <el-button
              link
              @click="viewAllNotifications"
              style="cursor: pointer !important;"
            >
              查看全部通知
            </el-button>
          </div>
        </div>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Bell } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { useNotificationStore } from '@/stores/modules/notification'
import { useUserStore } from '@/stores/modules/user'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'dropdown' // 'dropdown' or 'drawer'
  }
})

// Emits
const emit = defineEmits(['update:visible', 'view-task'])
import NotificationList from './NotificationList.vue'
import { notificationService } from '@/utils/notification-service'
import { notificationApi } from '@/api/notification'
import { ElMessage } from 'element-plus'

const router = useRouter()
const notificationStore = useNotificationStore()
const userStore = useUserStore()
const activeTab = ref('all')

// 计算属性
const notifications = computed(() => notificationStore.notifications)
const unreadCount = computed(() => notificationStore.unreadCount)
const hasUnread = computed(() => notificationStore.hasUnread)
const isDrawerMode = computed(() => props.mode === 'drawer')

// 过滤出任务相关通知
const taskNotifications = computed(() => {
  return notifications.value.filter(notification => {
    const taskTypes = ['TaskAssigned', 'TaskStatusChanged', 'TaskComment', 'TaskAttachmentAdded', 'TaskMention', 'TaskOverdue', 'TaskContentChanged']
    return taskTypes.includes(notification.type)
  })
})

// 过滤出系统通知
const systemNotifications = computed(() => {
  return notifications.value.filter(notification => {
    const taskTypes = ['TaskAssigned', 'TaskStatusChanged', 'TaskComment', 'TaskAttachmentAdded', 'TaskMention', 'TaskOverdue', 'TaskContentChanged']
    return !taskTypes.includes(notification.type)
  })
})



// 事件处理函数
const onVisibleChange = async (visible) => {
  console.log('通知中心可见性变化:', visible)
  if (visible) {
    try {
      console.log('通知中心打开，开始获取通知...')
      await notificationStore.fetchNotifications(true) // 强制刷新
      console.log('通知数据获取完成:', notificationStore.notifications.length, '条通知')
      console.log('通知详情:', notificationStore.notifications)
    } catch (error) {
      console.error('加载通知失败', error)
    }
  }
}

const handleTabClick = () => {
  // 切换标签时的处理逻辑
}

const markAsRead = async (notificationId) => {
  try {
    console.log('🔔 标记通知为已读:', notificationId)
    await notificationStore.markAsRead(notificationId)
    console.log('🔔 标记成功')
  } catch (error) {
    console.error('🔔 标记通知为已读失败', error)
  }
}

const markAllAsRead = async () => {
  try {
    await notificationStore.markAllAsRead()
  } catch (error) {
    console.error('标记所有通知为已读失败', error)
  }
}

const handleNotificationClick = (notification) => {
  console.log('🔔 通知点击事件:', notification)

  // 只有在通知未读时才标记为已读，避免重复调用
  if (!notification.read && !notification.isRead) {
    console.log('🔔 通知未读，标记为已读')
    markAsRead(notification.id)
  } else {
    console.log('🔔 通知已读，跳过标记')
  }

  // 获取任务ID
  const taskId = notification.referenceType === 'Task' ? notification.referenceId :
                notification.resourceType === 'Task' ? notification.resourceId :
                notification.taskId

  if (taskId) {
    console.log('🔔 跳转到任务详情:', taskId)
    if (isDrawerMode.value) {
      // drawer模式下发射事件给父组件处理
      emit('view-task', taskId)
    } else {
      // dropdown模式下直接跳转
      router.push(`/main/tasks/detail/${taskId}`)
    }
  }
}

const viewAllNotifications = () => {
  console.log('🔔 viewAllNotifications 被调用')
  console.log('🔔 isDrawerMode:', isDrawerMode.value)
  console.log('🔔 当前路由:', router.currentRoute.value.path)

  try {
    // 如果是drawer模式，先关闭drawer
    if (isDrawerMode.value) {
      console.log('🔔 发射 update:visible 事件')
      emit('update:visible', false)
    }

    console.log('🔔 开始路由跳转到 /main/notifications')
    router.push('/main/notifications').then(() => {
      console.log('🔔 路由跳转成功')
    }).catch(error => {
      console.error('🔔 路由跳转失败:', error)
    })
  } catch (error) {
    console.error('🔔 viewAllNotifications 执行出错:', error)
  }
}

const testRealTimeNotification = () => {
  console.log('🔔 测试实时通知')
  // 直接调用notification service的测试方法
  notificationService.sendTestNotification(userStore.userId)
}

// 生命周期钩子
onMounted(async () => {
  try {
    console.log('NotificationCenter onMounted, isDrawerMode:', isDrawerMode.value)

    // 无论什么模式都要获取通知列表
    console.log('开始获取通知列表...')
    await notificationStore.fetchNotifications(true) // 强制刷新
    console.log('通知获取完成，数量:', notificationStore.notifications.length)

    // 仅在dropdown模式下启动SignalR（轮询由用户登录后自动启动）
    if (!isDrawerMode.value) {
      console.log('启动SignalR连接...')
      // 通知轮询已由用户登录后自动启动，这里不重复启动
      // notificationStore.startPolling() // 已移除

      // 初始化SignalR连接
      if (userStore.userId) {
        await notificationService.initConnection(userStore.userId)
      }
    }
  } catch (error) {
    console.error('初始化通知组件失败', error)
  }
})

onUnmounted(() => {
  // 仅在dropdown模式下断开连接，drawer模式由DefaultLayout管理
  if (!isDrawerMode.value) {
    notificationService.disconnect()
  }
})
</script>

<style scoped>
.notification-center {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  z-index: 2000; /* 确保通知图标在最上层 */
}

.notification-trigger {
  display: flex;
  align-items: center;
}

.notification-badge {
  margin-right: 8px;
}

.notification-icon-button {
  font-size: 20px;
  cursor: pointer !important; /* 强制鼠标指针为可点击状态 */
}

.notification-dropdown-content {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 380px;
  max-height: 500px;
  overflow-y: auto;
  padding: 12px;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 8px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 8px;
}

.notification-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.notification-footer {
  display: flex;
  justify-content: center;
  padding-top: 8px;
  border-top: 1px solid #ebeef5;
  margin-top: 8px;
}
</style> 