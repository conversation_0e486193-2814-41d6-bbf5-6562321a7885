// File: frontend/src/api/asset-analytics.js
// Description: 资产分析工作台API接口

import request from '@/utils/request'

const API_BASE = '/v2/asset-statistics'

export const assetAnalyticsApi = {
  /**
   * 获取资产分析工作台完整数据
   * @returns {Promise} 工作台数据
   */
  getAnalyticsWorkbench() {
    return request({
      url: `${API_BASE}/analytics-workbench`,
      method: 'get'
    })
  },

  /**
   * 获取资产类型统计
   * @param {Object} params 查询参数
   * @returns {Promise} 类型统计数据
   */
  getTypeStatistics(params = {}) {
    return request({
      url: `${API_BASE}/type-statistics`,
      method: 'get',
      params
    })
  },

  /**
   * 获取区域统计
   * @param {Object} params 查询参数
   * @returns {Promise} 区域统计数据
   */
  getRegionStatistics(params = {}) {
    return request({
      url: `${API_BASE}/region-statistics`,
      method: 'get',
      params
    })
  },

  /**
   * 获取部门统计
   * @param {Object} params 查询参数
   * @returns {Promise} 部门统计数据
   */
  getDepartmentStatistics(params = {}) {
    return request({
      url: `${API_BASE}/department-statistics`,
      method: 'get',
      params
    })
  },

  /**
   * 获取资产类型选项
   * @returns {Promise} 资产类型列表
   */
  getAssetTypes() {
    return request({
      url: `${API_BASE}/asset-types`,
      method: 'get'
    })
  },

  /**
   * 获取区域选项
   * @returns {Promise} 区域列表
   */
  getRegions() {
    return request({
      url: `${API_BASE}/regions`,
      method: 'get'
    })
  },

  /**
   * 获取部门选项
   * @returns {Promise} 部门列表
   */
  getDepartments() {
    return request({
      url: `${API_BASE}/departments`,
      method: 'get'
    })
  },

  /**
   * 获取周趋势数据
   * @param {Object} query 查询参数
   * @returns {Promise} 周趋势数据
   */
  getWeeklyTrend(query = {}) {
    return request({
      url: `${API_BASE}/weekly-trend`,
      method: 'get',
      params: query
    })
  },

  /**
   * 获取日趋势数据
   * @param {Object} query 查询参数
   * @returns {Promise} 日趋势数据
   */
  getDailyTrend(query = {}) {
    return request({
      url: `${API_BASE}/daily-trend`,
      method: 'get',
      params: query
    })
  },

  /**
   * 获取月趋势数据
   * @param {Object} query 查询参数
   * @returns {Promise} 月趋势数据
   */
  getMonthlyTrend(query = {}) {
    return request({
      url: `${API_BASE}/monthly-trend`,
      method: 'get',
      params: query
    })
  },

  /**
   * 获取组合统计数据
   * @param {Object} query 查询参数
   * @returns {Promise} 组合统计数据
   */
  getCombinedStatistics(query = {}) {
    return request({
      url: `${API_BASE}/combined-statistics`,
      method: 'get',
      params: query
    })
  }
}

export default assetAnalyticsApi
