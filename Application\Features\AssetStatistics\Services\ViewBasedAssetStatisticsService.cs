using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.AssetStatistics.Dtos;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Data.Common;

namespace ItAssetsSystem.Application.Features.AssetStatistics.Services
{
    /// <summary>
    /// 基于数据库视图的高性能资产统计服务
    /// 版本: v3.0 (生产优化版 - 移除所有降级方案)
    /// 特性: 直接使用优化视图，确保返回真实数据
    /// </summary>
    public class ViewBasedAssetStatisticsService : IAssetStatisticsService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<ViewBasedAssetStatisticsService> _logger;

        public ViewBasedAssetStatisticsService(
            AppDbContext context,
            ILogger<ViewBasedAssetStatisticsService> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 获取资产分析工作台完整数据 - 核心API
        /// </summary>
        public async Task<ApiResponse<AssetAnalyticsWorkbenchDto>> GetAnalyticsWorkbenchDataAsync()
        {
            try
            {
                _logger.LogInformation("获取工作台数据 - 使用优化视图");

                // 并行获取所有数据组件
                var kpiTask = GetKpiDataDirectAsync();
                var typeStatsTask = GetTypeStatisticsDirectAsync();
                var regionStatsTask = GetRegionStatisticsDirectAsync();
                var deptStatsTask = GetDepartmentStatisticsDirectAsync();
                var timeSeriesTask = GetTimeSeriesDataDirectAsync();
                var valueDistTask = GetValueDistributionDirectAsync();
                var matrixTask = GetMatrixDataDirectAsync();
                var statusDistTask = GetStatusDistributionDirectAsync();
                var filterOptionsTask = GetFilterOptionsDirectAsync();

                await Task.WhenAll(kpiTask, typeStatsTask, regionStatsTask, deptStatsTask, 
                    timeSeriesTask, valueDistTask, matrixTask, statusDistTask, filterOptionsTask);

                var workbenchData = new AssetAnalyticsWorkbenchDto
                {
                    KpiData = await kpiTask,
                    TypeStatistics = await typeStatsTask,
                    RegionStatistics = await regionStatsTask,
                    DepartmentStatistics = await deptStatsTask,
                    TimeSeriesData = await timeSeriesTask,
                    ValueDistribution = await valueDistTask,
                    MatrixData = await matrixTask,
                    StatusDistribution = await statusDistTask,
                    FilterOptions = await filterOptionsTask,
                    UpdateTime = DateTime.Now
                };

                _logger.LogInformation("工作台数据获取成功，KPI总资产数：{TotalAssets}", workbenchData.KpiData.TotalAssets);
                return ApiResponse<AssetAnalyticsWorkbenchDto>.CreateSuccess(workbenchData, "获取工作台数据成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取工作台数据失败");
                throw; // 不再使用降级方案，直接抛出异常让上层处理
            }
        }

        /// <summary>
        /// 直接从KPI视图获取数据
        /// </summary>
        private async Task<AssetKpiDataDto> GetKpiDataDirectAsync()
        {
            var sql = @"
                SELECT
                    TotalAssets,
                    TotalValueWan,
                    InUseRate,
                    IdleRate,
                    MaintenanceRate,
                    FaultRate,
                    ScrappedRate,
                    AverageUtilization,
                    HealthScore,
                    FaultAssets,
                    TypeCount,
                    DepartmentCount,
                    RegionCount
                FROM v_asset_kpi_enhanced
                LIMIT 1";

            using var command = _context.Database.GetDbConnection().CreateCommand();
            command.CommandText = sql;
            
            if (command.Connection.State != System.Data.ConnectionState.Open)
                await command.Connection.OpenAsync();

            using var reader = await command.ExecuteReaderAsync();
            
            if (await reader.ReadAsync())
            {
                return new AssetKpiDataDto
                {
                    TotalAssets = Convert.ToInt32(reader["TotalAssets"]),
                    TotalValue = Convert.ToDecimal(reader["TotalValueWan"]),
                    OnlineRate = Convert.ToDouble(reader["InUseRate"]),
                    IdleRate = Convert.ToDouble(reader["IdleRate"]),
                    MaintenanceRate = Convert.ToDouble(reader["MaintenanceRate"]),
                    FaultCount = Convert.ToInt32(reader["FaultAssets"]),
                    AverageUtilization = Convert.ToDouble(reader["AverageUtilization"]),
                    HealthScore = Convert.ToDouble(reader["HealthScore"]),
                    TotalTrend = 0, // 趋势数据暂不实现
                    ValueTrend = 0,
                    OnlineRateTrend = 0,
                    FaultTrend = 0
                };
            }

            throw new InvalidOperationException("KPI视图返回空数据，请检查数据库");
        }

        /// <summary>
        /// 直接从统计视图获取类型统计
        /// </summary>
        private async Task<List<AssetTypeStatisticsDto>> GetTypeStatisticsDirectAsync()
        {
            var sql = @"
                SELECT
                    CAST(DimensionKey AS UNSIGNED) as AssetTypeId,
                    DimensionName as AssetTypeName,
                    TotalCount as AssetCount,
                    InUseCount as NormalCount,
                    FaultCount,
                    MaintenanceCount,
                    InUseRate as NormalRate,
                    FaultRate,
                    Percentage
                FROM v_asset_statistics_fast
                WHERE DimensionType = 'type'
                ORDER BY TotalCount DESC
                LIMIT 20";

            return await ExecuteListQueryAsync<AssetTypeStatisticsDto>(sql, reader => new AssetTypeStatisticsDto
            {
                AssetTypeId = Convert.ToInt32(reader["AssetTypeId"]),
                AssetTypeName = Convert.ToString(reader["AssetTypeName"]),
                AssetCount = Convert.ToInt32(reader["AssetCount"]),
                NormalCount = Convert.ToInt32(reader["NormalCount"]),
                FaultCount = Convert.ToInt32(reader["FaultCount"]),
                MaintenanceCount = Convert.ToInt32(reader["MaintenanceCount"]),
                NormalRate = Convert.ToDecimal(reader["NormalRate"]),
                FaultRate = Convert.ToDecimal(reader["FaultRate"]),
                Percentage = Convert.ToDecimal(reader["Percentage"])
            });
        }

        /// <summary>
        /// 直接从统计视图获取区域统计
        /// </summary>
        private async Task<List<AssetRegionStatisticsDto>> GetRegionStatisticsDirectAsync()
        {
            var sql = @"
                SELECT
                    CAST(DimensionKey AS UNSIGNED) as RegionId,
                    DimensionName as RegionName,
                    TotalCount as AssetCount,
                    InUseCount as NormalCount,
                    FaultCount,
                    MaintenanceCount,
                    InUseRate as NormalRate,
                    FaultRate,
                    Percentage
                FROM v_asset_statistics_fast
                WHERE DimensionType = 'region'
                ORDER BY TotalCount DESC
                LIMIT 20";

            return await ExecuteListQueryAsync<AssetRegionStatisticsDto>(sql, reader => new AssetRegionStatisticsDto
            {
                RegionId = Convert.ToInt32(reader["RegionId"]),
                RegionName = Convert.ToString(reader["RegionName"]),
                AssetCount = Convert.ToInt32(reader["AssetCount"]),
                NormalCount = Convert.ToInt32(reader["NormalCount"]),
                FaultCount = Convert.ToInt32(reader["FaultCount"]),
                MaintenanceCount = Convert.ToInt32(reader["MaintenanceCount"]),
                NormalRate = Convert.ToDecimal(reader["NormalRate"]),
                FaultRate = Convert.ToDecimal(reader["FaultRate"]),
                Percentage = Convert.ToDecimal(reader["Percentage"])
            });
        }

        /// <summary>
        /// 直接从统计视图获取部门统计
        /// </summary>
        private async Task<List<AssetDepartmentStatisticsDto>> GetDepartmentStatisticsDirectAsync()
        {
            var sql = @"
                SELECT
                    CAST(DimensionKey AS UNSIGNED) as DepartmentId,
                    DimensionName as DepartmentName,
                    TotalCount as AssetCount,
                    InUseCount as NormalCount,
                    FaultCount,
                    MaintenanceCount,
                    InUseRate as NormalRate,
                    FaultRate,
                    Percentage
                FROM v_asset_statistics_fast
                WHERE DimensionType = 'department'
                ORDER BY TotalCount DESC
                LIMIT 20";

            return await ExecuteListQueryAsync<AssetDepartmentStatisticsDto>(sql, reader => new AssetDepartmentStatisticsDto
            {
                DepartmentId = Convert.ToInt32(reader["DepartmentId"]),
                DepartmentName = Convert.ToString(reader["DepartmentName"]),
                AssetCount = Convert.ToInt32(reader["AssetCount"]),
                NormalCount = Convert.ToInt32(reader["NormalCount"]),
                FaultCount = Convert.ToInt32(reader["FaultCount"]),
                MaintenanceCount = Convert.ToInt32(reader["MaintenanceCount"]),
                NormalRate = Convert.ToDecimal(reader["NormalRate"]),
                FaultRate = Convert.ToDecimal(reader["FaultRate"]),
                Percentage = Convert.ToDecimal(reader["Percentage"])
            });
        }

        /// <summary>
        /// 获取基于真实数据的时间序列
        /// </summary>
        private async Task<AssetTimeSeriesDataDto> GetTimeSeriesDataDirectAsync()
        {
            var sql = @"
                SELECT
                    CASE 
                        WHEN HOUR(CreatedAt) BETWEEN 0 AND 3 THEN 0
                        WHEN HOUR(CreatedAt) BETWEEN 4 AND 7 THEN 1
                        WHEN HOUR(CreatedAt) BETWEEN 8 AND 11 THEN 2
                        WHEN HOUR(CreatedAt) BETWEEN 12 AND 15 THEN 3
                        WHEN HOUR(CreatedAt) BETWEEN 16 AND 19 THEN 4
                        ELSE 5
                    END as TimeSlot,
                    SUM(CASE WHEN Status = 1 THEN 1 ELSE 0 END) as OnlineCount,
                    SUM(CASE WHEN Status = 0 THEN 1 ELSE 0 END) as OfflineCount,
                    SUM(CASE WHEN Status = 2 THEN 1 ELSE 0 END) as MaintenanceCount
                FROM assets
                WHERE CreatedAt >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY TimeSlot
                ORDER BY TimeSlot";

            var timeLabels = new List<string> { "00:00", "04:00", "08:00", "12:00", "16:00", "20:00" };
            var onlineData = new List<int> { 0, 0, 0, 0, 0, 0 };
            var offlineData = new List<int> { 0, 0, 0, 0, 0, 0 };
            var maintenanceData = new List<int> { 0, 0, 0, 0, 0, 0 };

            using var command = _context.Database.GetDbConnection().CreateCommand();
            command.CommandText = sql;
            
            if (command.Connection.State != System.Data.ConnectionState.Open)
                await command.Connection.OpenAsync();

            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                var slot = Convert.ToInt32(reader["TimeSlot"]);
                if (slot >= 0 && slot < 6)
                {
                    onlineData[slot] = Convert.ToInt32(reader["OnlineCount"]);
                    offlineData[slot] = Convert.ToInt32(reader["OfflineCount"]);
                    maintenanceData[slot] = Convert.ToInt32(reader["MaintenanceCount"]);
                }
            }

            return new AssetTimeSeriesDataDto
            {
                TimeLabels = timeLabels,
                OnlineData = onlineData,
                OfflineData = offlineData,
                MaintenanceData = maintenanceData
            };
        }

        /// <summary>
        /// 直接从价值分布视图获取数据
        /// </summary>
        private async Task<AssetValueDistributionDto> GetValueDistributionDirectAsync()
        {
            var sql = @"
                SELECT
                    ValueRange,
                    AssetCount,
                    TotalValueWan,
                    RangeColor
                FROM v_asset_value_distribution_enhanced
                ORDER BY SortOrder";

            var valueRanges = new List<string>();
            var assetCounts = new List<int>();
            var totalValues = new List<decimal>();
            var rangeColors = new List<string>();

            using var command = _context.Database.GetDbConnection().CreateCommand();
            command.CommandText = sql;
            
            if (command.Connection.State != System.Data.ConnectionState.Open)
                await command.Connection.OpenAsync();

            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                valueRanges.Add(Convert.ToString(reader["ValueRange"]));
                assetCounts.Add(Convert.ToInt32(reader["AssetCount"]));
                totalValues.Add(Convert.ToDecimal(reader["TotalValueWan"]));
                rangeColors.Add(Convert.ToString(reader["RangeColor"]));
            }

            return new AssetValueDistributionDto
            {
                ValueRanges = valueRanges,
                AssetCounts = assetCounts,
                TotalValues = totalValues,
                RangeColors = rangeColors
            };
        }

        /// <summary>
        /// 获取基于真实数据的矩阵分析
        /// </summary>
        private async Task<AssetMatrixDataDto> GetMatrixDataDirectAsync()
        {
            var sql = @"
                SELECT
                    DepartmentName,
                    AssetTypeName,
                    AssetCount
                FROM v_asset_matrix_enhanced
                ORDER BY DepartmentName, AssetTypeName";

            var matrixDict = new Dictionary<string, Dictionary<string, int>>();
            var departments = new HashSet<string>();
            var assetTypes = new HashSet<string>();

            using var command = _context.Database.GetDbConnection().CreateCommand();
            command.CommandText = sql;
            
            if (command.Connection.State != System.Data.ConnectionState.Open)
                await command.Connection.OpenAsync();

            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                var dept = Convert.ToString(reader["DepartmentName"]);
                var type = Convert.ToString(reader["AssetTypeName"]);
                var count = Convert.ToInt32(reader["AssetCount"]);

                departments.Add(dept);
                assetTypes.Add(type);

                if (!matrixDict.ContainsKey(dept))
                    matrixDict[dept] = new Dictionary<string, int>();
                
                matrixDict[dept][type] = count;
            }

            // 构建矩阵
            var deptList = departments.OrderBy(d => d).ToList();
            var typeList = assetTypes.OrderBy(t => t).ToList();
            var matrix = new List<List<int>>();

            foreach (var dept in deptList)
            {
                var row = new List<int>();
                foreach (var type in typeList)
                {
                    var count = matrixDict.ContainsKey(dept) && matrixDict[dept].ContainsKey(type)
                        ? matrixDict[dept][type] : 0;
                    row.Add(count);
                }
                matrix.Add(row);
            }

            return new AssetMatrixDataDto
            {
                Departments = deptList,
                AssetTypes = typeList,
                MatrixData = matrix
            };
        }

        /// <summary>
        /// 直接从统计视图获取状态分布
        /// </summary>
        private async Task<AssetStatusDistributionDto> GetStatusDistributionDirectAsync()
        {
            var sql = @"
                SELECT
                    DimensionName as StatusLabel,
                    TotalCount as StatusCount,
                    Percentage as StatusPercentage
                FROM v_asset_statistics_fast
                WHERE DimensionType = 'status'
                ORDER BY FIELD(DimensionKey, '1', '0', '2', '4', '3')";

            var statusLabels = new List<string>();
            var statusCounts = new List<int>();
            var statusPercentages = new List<double>();

            using var command = _context.Database.GetDbConnection().CreateCommand();
            command.CommandText = sql;
            
            if (command.Connection.State != System.Data.ConnectionState.Open)
                await command.Connection.OpenAsync();

            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                statusLabels.Add(Convert.ToString(reader["StatusLabel"]));
                statusCounts.Add(Convert.ToInt32(reader["StatusCount"]));
                statusPercentages.Add(Convert.ToDouble(reader["StatusPercentage"]));
            }

            return new AssetStatusDistributionDto
            {
                StatusLabels = statusLabels,
                StatusCounts = statusCounts,
                StatusPercentages = statusPercentages
            };
        }

        /// <summary>
        /// 获取筛选选项 - 基于真实数据
        /// </summary>
        private async Task<AssetFilterOptionsDto> GetFilterOptionsDirectAsync()
        {
            var assetTypes = await GetAssetTypesDirectAsync();
            var regions = await GetRegionOptionsDirectAsync();
            var departments = await GetDepartmentOptionsDirectAsync();

            return new AssetFilterOptionsDto
            {
                AssetTypes = assetTypes,
                Regions = regions,
                Departments = departments,
                Statuses = new List<AssetStatusOptionDto>
                {
                    new() { Value = 1, Label = "在用", Color = "#10b981" },
                    new() { Value = 0, Label = "闲置", Color = "#f59e0b" },
                    new() { Value = 2, Label = "维修中", Color = "#3b82f6" },
                    new() { Value = 4, Label = "故障", Color = "#ef4444" },
                    new() { Value = 3, Label = "报废", Color = "#6b7280" }
                }
            };
        }

        /// <summary>
        /// 获取资产类型选项 - 基于真实数据
        /// </summary>
        private async Task<List<AssetTypeDto>> GetAssetTypesDirectAsync()
        {
            var sql = @"
                SELECT DISTINCT
                    AssetTypeId,
                    AssetTypeName,
                    COUNT(*) as AssetCount
                FROM v_assets_enhanced
                WHERE AssetTypeId > 0 AND AssetTypeName IS NOT NULL
                GROUP BY AssetTypeId, AssetTypeName
                ORDER BY AssetTypeName";

            return await ExecuteListQueryAsync<AssetTypeDto>(sql, reader => new AssetTypeDto
            {
                AssetTypeId = Convert.ToInt32(reader["AssetTypeId"]),
                AssetTypeName = Convert.ToString(reader["AssetTypeName"]),
                Description = "",
                IsActive = true,
                AssetCount = Convert.ToInt32(reader["AssetCount"])
            });
        }

        /// <summary>
        /// 获取区域选项 - 基于真实数据
        /// </summary>
        private async Task<List<AssetRegionDto>> GetRegionOptionsDirectAsync()
        {
            var sql = @"
                SELECT DISTINCT
                    RegionId,
                    RegionName,
                    COUNT(*) as AssetCount
                FROM v_assets_enhanced
                WHERE RegionId > 0 AND RegionName IS NOT NULL
                GROUP BY RegionId, RegionName
                ORDER BY RegionName";

            return await ExecuteListQueryAsync<AssetRegionDto>(sql, reader => new AssetRegionDto
            {
                RegionId = Convert.ToInt32(reader["RegionId"]),
                RegionName = Convert.ToString(reader["RegionName"]),
                Description = "",
                IsActive = true,
                AssetCount = Convert.ToInt32(reader["AssetCount"])
            });
        }

        /// <summary>
        /// 获取部门选项 - 基于真实数据
        /// </summary>
        private async Task<List<AssetDepartmentDto>> GetDepartmentOptionsDirectAsync()
        {
            var sql = @"
                SELECT DISTINCT
                    InheritedDepartmentId as DepartmentId,
                    DepartmentName,
                    COUNT(*) as AssetCount
                FROM v_assets_enhanced
                WHERE InheritedDepartmentId > 0 AND DepartmentName IS NOT NULL
                GROUP BY InheritedDepartmentId, DepartmentName
                ORDER BY DepartmentName";

            return await ExecuteListQueryAsync<AssetDepartmentDto>(sql, reader => new AssetDepartmentDto
            {
                DepartmentId = Convert.ToInt32(reader["DepartmentId"]),
                DepartmentName = Convert.ToString(reader["DepartmentName"]),
                Description = "",
                IsActive = true,
                AssetCount = Convert.ToInt32(reader["AssetCount"])
            });
        }

        /// <summary>
        /// 通用的列表查询执行方法
        /// </summary>
        private async Task<List<T>> ExecuteListQueryAsync<T>(string sql, Func<DbDataReader, T> mapper)
        {
            var results = new List<T>();

            using var command = _context.Database.GetDbConnection().CreateCommand();
            command.CommandText = sql;
            
            if (command.Connection.State != System.Data.ConnectionState.Open)
                await command.Connection.OpenAsync();

            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                results.Add(mapper(reader));
            }

            return results;
        }

        // ================== IAssetStatisticsService 接口实现 ==================

        public async Task<ApiResponse<AssetOverallStatisticsDto>> GetOverallStatisticsAsync(AssetStatisticsQueryDto query)
        {
            var kpiData = await GetKpiDataDirectAsync();
            var overallStats = new AssetOverallStatisticsDto
            {
                TotalAssets = kpiData.TotalAssets,
                NormalAssets = (int)(kpiData.TotalAssets * kpiData.OnlineRate / 100),
                FaultAssets = kpiData.FaultCount,
                MaintenanceAssets = (int)(kpiData.TotalAssets * kpiData.MaintenanceRate / 100),
                IdleAssets = (int)(kpiData.TotalAssets * kpiData.IdleRate / 100),
                NormalRate = (decimal)kpiData.OnlineRate,
                FaultRate = kpiData.TotalAssets > 0 ? Math.Round((decimal)kpiData.FaultCount * 100 / kpiData.TotalAssets, 2) : 0,
                AssetTypeCount = (await GetAssetTypesDirectAsync()).Count,
                LocationCount = (await GetRegionOptionsDirectAsync()).Count,
                DepartmentCount = (await GetDepartmentOptionsDirectAsync()).Count
            };

            return ApiResponse<AssetOverallStatisticsDto>.CreateSuccess(overallStats, "获取总体统计成功");
        }

        public async Task<ApiResponse<List<AssetTypeStatisticsDto>>> GetStatisticsByTypeAsync(AssetStatisticsQueryDto query)
        {
            var typeStats = await GetTypeStatisticsDirectAsync();
            return ApiResponse<List<AssetTypeStatisticsDto>>.CreateSuccess(typeStats, "获取按类型统计成功");
        }

        public async Task<ApiResponse<List<AssetRegionStatisticsDto>>> GetStatisticsByRegionAsync(AssetStatisticsQueryDto query)
        {
            var regionStats = await GetRegionStatisticsDirectAsync();
            return ApiResponse<List<AssetRegionStatisticsDto>>.CreateSuccess(regionStats, "获取按区域统计成功");
        }

        public async Task<ApiResponse<List<AssetDepartmentStatisticsDto>>> GetStatisticsByDepartmentAsync(AssetStatisticsQueryDto query)
        {
            var deptStats = await GetDepartmentStatisticsDirectAsync();
            return ApiResponse<List<AssetDepartmentStatisticsDto>>.CreateSuccess(deptStats, "获取按部门统计成功");
        }

        public async Task<ApiResponse<List<AssetTrendDataDto>>> GetWeeklyTrendAsync(AssetTrendQueryDto query) =>
            ApiResponse<List<AssetTrendDataDto>>.CreateSuccess(new List<AssetTrendDataDto>(), "趋势分析暂未实现");

        public async Task<ApiResponse<List<AssetTrendDataDto>>> GetDailyTrendAsync(AssetTrendQueryDto query) =>
            ApiResponse<List<AssetTrendDataDto>>.CreateSuccess(new List<AssetTrendDataDto>(), "趋势分析暂未实现");

        public async Task<ApiResponse<List<AssetTrendDataDto>>> GetMonthlyTrendAsync(AssetTrendQueryDto query) =>
            ApiResponse<List<AssetTrendDataDto>>.CreateSuccess(new List<AssetTrendDataDto>(), "趋势分析暂未实现");

        public async Task<ApiResponse<AssetCombinedStatisticsDto>> GetCombinedStatisticsAsync(AssetStatisticsQueryDto query) =>
            ApiResponse<AssetCombinedStatisticsDto>.CreateSuccess(new AssetCombinedStatisticsDto(), "组合统计暂未实现");

        public async Task<ApiResponse<List<AssetTypeDto>>> GetAssetTypesAsync()
        {
            var assetTypes = await GetAssetTypesDirectAsync();
            return ApiResponse<List<AssetTypeDto>>.CreateSuccess(assetTypes, "获取资产类型成功");
        }

        public async Task<ApiResponse<List<AssetRegionDto>>> GetRegionsAsync()
        {
            var regions = await GetRegionOptionsDirectAsync();
            return ApiResponse<List<AssetRegionDto>>.CreateSuccess(regions, "获取区域列表成功");
        }

        public async Task<ApiResponse<List<AssetDepartmentDto>>> GetDepartmentsAsync()
        {
            var departments = await GetDepartmentOptionsDirectAsync();
            return ApiResponse<List<AssetDepartmentDto>>.CreateSuccess(departments, "获取部门列表成功");
        }

        public async Task<ApiResponse<List<AssetRegionDto>>> GetRegionOptionsAsync()
        {
            var regions = await GetRegionOptionsDirectAsync();
            return ApiResponse<List<AssetRegionDto>>.CreateSuccess(regions, "获取区域选项成功");
        }

        public async Task<ApiResponse<List<AssetDepartmentDto>>> GetDepartmentOptionsAsync()
        {
            var departments = await GetDepartmentOptionsDirectAsync();
            return ApiResponse<List<AssetDepartmentDto>>.CreateSuccess(departments, "获取部门选项成功");
        }
    }
}