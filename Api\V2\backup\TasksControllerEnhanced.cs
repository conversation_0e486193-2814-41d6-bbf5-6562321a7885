// File: Api/V2/Controllers/TasksControllerEnhanced.cs
// Description: 增强的任务管理API控制器，支持批量操作、实时通知等现代化功能

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Application.Features.Tasks.Services;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Core.Services;
using ItAssetsSystem.Models;

namespace ItAssetsSystem.Api.V2.Controllers
{
    /// <summary>
    /// 增强的任务管理API控制器
    /// </summary>
    [ApiController]
    [Route("api/v2/tasks-enhanced")]
    [Authorize]
    public class TasksControllerEnhanced : ControllerBase
    {
        private readonly ITaskService _taskService;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<TasksControllerEnhanced> _logger;

        public TasksControllerEnhanced(
            ITaskService taskService,
            ICurrentUserService currentUserService,
            ILogger<TasksControllerEnhanced> logger)
        {
            _taskService = taskService ?? throw new ArgumentNullException(nameof(taskService));
            _currentUserService = currentUserService ?? throw new ArgumentNullException(nameof(currentUserService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #region 基础CRUD操作

        /// <summary>
        /// 获取任务列表（支持分页、搜索、筛选）
        /// </summary>
        /// <param name="parameters">查询参数</param>
        /// <returns>任务列表</returns>
        [HttpGet]
        public async Task<ActionResult<ApiResponse<List<TaskDto>>>> GetTasks(TaskQueryParametersDto parameters)
        {
            try
            {
                var result = await _taskService.GetTasksAsync(parameters);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务列表时发生错误");
                return StatusCode(500, ApiResponseFactory.CreateFail<List<TaskDto>>("服务器内部错误"));
            }
        }

        /// <summary>
        /// 根据ID获取任务详情
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>任务详情</returns>
        [HttpGet("{id:long}")]
        public async Task<ActionResult<ApiResponse<TaskDto>>> GetTask(long id)
        {
            try
            {
                var result = await _taskService.GetTaskByIdAsync(id);
                if (!result.Success)
                {
                    return NotFound(result);
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务详情时发生错误，TaskId: {TaskId}", id);
                return StatusCode(500, ApiResponseFactory.CreateFail<TaskDto>("服务器内部错误"));
            }
        }

        /// <summary>
        /// 创建新任务
        /// </summary>
        /// <param name="request">创建任务请求</param>
        /// <returns>创建的任务</returns>
        [HttpPost]
        public async Task<ActionResult<ApiResponse<TaskDto>>> CreateTask(CreateTaskRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<TaskDto>("请求参数无效"));
                }

                var currentUserId = _currentUserService.GetCurrentUserId();
                var result = await _taskService.CreateTaskAsync(request, currentUserId);
                
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                return CreatedAtAction(nameof(GetTask), new { id = result.Data.TaskId }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建任务时发生错误");
                return StatusCode(500, ApiResponseFactory.CreateFail<TaskDto>("服务器内部错误"));
            }
        }

        /// <summary>
        /// 更新任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="request">更新任务请求</param>
        /// <returns>更新后的任务</returns>
        [HttpPut("{id:long}")]
        public async Task<ActionResult<ApiResponse<TaskDto>>> UpdateTask(long id, UpdateTaskRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<TaskDto>("请求参数无效"));
                }

                var currentUserId = _currentUserService.GetCurrentUserId();
                var result = await _taskService.UpdateTaskAsync(id, request, currentUserId);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务时发生错误，TaskId: {TaskId}", id);
                return StatusCode(500, ApiResponseFactory.CreateFail<TaskDto>("服务器内部错误"));
            }
        }

        /// <summary>
        /// 删除任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id:long}")]
        public async Task<ActionResult<ApiResponse<bool>>> DeleteTask(long id)
        {
            try
            {
                var currentUserId = _currentUserService.GetCurrentUserId();
                var result = await _taskService.DeleteTaskAsync(id, currentUserId);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除任务时发生错误，TaskId: {TaskId}", id);
                return StatusCode(500, ApiResponseFactory.CreateFail<bool>("服务器内部错误"));
            }
        }

        #endregion

        #region 任务状态管理

        /// <summary>
        /// 更新任务状态
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="request">状态更新请求</param>
        /// <returns>更新后的任务</returns>
        [HttpPatch("{id:long}/status")]
        public async Task<ActionResult<ApiResponse<TaskDto>>> UpdateTaskStatus(long id, UpdateTaskStatusRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<TaskDto>("请求参数无效"));
                }

                var currentUserId = _currentUserService.GetCurrentUserId();
                var result = await _taskService.UpdateTaskStatusAsync(id, request.Status, request.Remarks, currentUserId);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务状态时发生错误，TaskId: {TaskId}", id);
                return StatusCode(500, ApiResponseFactory.CreateFail<TaskDto>("服务器内部错误"));
            }
        }

        /// <summary>
        /// 更新任务进度
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="request">进度更新请求</param>
        /// <returns>更新后的任务</returns>
        [HttpPatch("{id:long}/progress")]
        public async Task<ActionResult<ApiResponse<TaskDto>>> UpdateTaskProgress(long id, UpdateTaskProgressRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<TaskDto>("请求参数无效"));
                }

                var currentUserId = _currentUserService.GetCurrentUserId();
                var result = await _taskService.UpdateTaskProgressAsync(id, request.Progress, request.Remarks, currentUserId);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务进度时发生错误，TaskId: {TaskId}", id);
                return StatusCode(500, ApiResponseFactory.CreateFail<TaskDto>("服务器内部错误"));
            }
        }

        /// <summary>
        /// 分配任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="request">分配请求</param>
        /// <returns>分配后的任务</returns>
        [HttpPatch("{id:long}/assign")]
        public async Task<ActionResult<ApiResponse<TaskDto>>> AssignTask(long id, AssignTaskRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<TaskDto>("请求参数无效"));
                }

                var currentUserId = _currentUserService.GetCurrentUserId();
                var result = await _taskService.AssignTaskAsync(id, request.AssigneeUserId, request.Remarks, currentUserId);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分配任务时发生错误，TaskId: {TaskId}", id);
                return StatusCode(500, ApiResponseFactory.CreateFail<TaskDto>("服务器内部错误"));
            }
        }

        /// <summary>
        /// 完成任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="request">完成请求</param>
        /// <returns>完成的任务</returns>
        [HttpPatch("{id:long}/complete")]
        public async Task<ActionResult<ApiResponse<TaskDto>>> CompleteTask(long id, CompleteTaskRequestDto request)
        {
            try
            {
                var currentUserId = _currentUserService.GetCurrentUserId();
                var result = await _taskService.CompleteTaskAsync(id, request?.Remarks ?? string.Empty, currentUserId);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "完成任务时发生错误，TaskId: {TaskId}", id);
                return StatusCode(500, ApiResponseFactory.CreateFail<TaskDto>("服务器内部错误"));
            }
        }

        #endregion

        #region 批量操作

        /// <summary>
        /// 批量更新任务状态
        /// </summary>
        /// <param name="request">批量状态更新请求</param>
        /// <returns>操作结果</returns>
        [HttpPatch("batch/status")]
        public async Task<ActionResult<ApiResponse<BatchOperationResultDto>>> BatchUpdateStatus(BatchUpdateStatusRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid || !request.TaskIds.Any())
                {
                    return BadRequest(ApiResponseFactory.CreateFail<BatchOperationResultDto>("请求参数无效"));
                }

                var currentUserId = _currentUserService.GetCurrentUserId();
                var results = new List<BatchOperationItemResult>();

                foreach (var taskId in request.TaskIds)
                {
                    try
                    {
                        var result = await _taskService.UpdateTaskStatusAsync(taskId, request.Status, request.Remarks, currentUserId);
                        results.Add(new BatchOperationItemResult
                        {
                            ItemId = taskId.ToString(),
                            Success = result.Success,
                            Message = result.Message
                        });
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "批量更新状态失败，TaskId: {TaskId}", taskId);
                        results.Add(new BatchOperationItemResult
                        {
                            ItemId = taskId.ToString(),
                            Success = false,
                            Message = "操作失败: " + ex.Message
                        });
                    }
                }

                var batchResult = new BatchOperationResultDto
                {
                    TotalCount = request.TaskIds.Count,
                    SuccessCount = results.Count(r => r.Success),
                    FailureCount = results.Count(r => !r.Success),
                    Results = results
                };

                return Ok(ApiResponseFactory.CreateSuccess(batchResult, "批量操作完成"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新任务状态时发生错误");
                return StatusCode(500, ApiResponseFactory.CreateFail<BatchOperationResultDto>("服务器内部错误"));
            }
        }

        /// <summary>
        /// 批量分配任务
        /// </summary>
        /// <param name="request">批量分配请求</param>
        /// <returns>操作结果</returns>
        [HttpPatch("batch/assign")]
        public async Task<ActionResult<ApiResponse<BatchOperationResultDto>>> BatchAssignTasks(BatchAssignRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid || !request.TaskIds.Any())
                {
                    return BadRequest(ApiResponseFactory.CreateFail<BatchOperationResultDto>("请求参数无效"));
                }

                var currentUserId = _currentUserService.GetCurrentUserId();
                var results = new List<BatchOperationItemResult>();

                foreach (var taskId in request.TaskIds)
                {
                    try
                    {
                        var result = await _taskService.AssignTaskAsync(taskId, request.AssigneeUserId, request.Remarks, currentUserId);
                        results.Add(new BatchOperationItemResult
                        {
                            ItemId = taskId.ToString(),
                            Success = result.Success,
                            Message = result.Message
                        });
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "批量分配失败，TaskId: {TaskId}", taskId);
                        results.Add(new BatchOperationItemResult
                        {
                            ItemId = taskId.ToString(),
                            Success = false,
                            Message = "操作失败: " + ex.Message
                        });
                    }
                }

                var batchResult = new BatchOperationResultDto
                {
                    TotalCount = request.TaskIds.Count,
                    SuccessCount = results.Count(r => r.Success),
                    FailureCount = results.Count(r => !r.Success),
                    Results = results
                };

                return Ok(ApiResponseFactory.CreateSuccess(batchResult, "批量分配完成"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量分配任务时发生错误");
                return StatusCode(500, ApiResponseFactory.CreateFail<BatchOperationResultDto>("服务器内部错误"));
            }
        }

        /// <summary>
        /// 批量删除任务
        /// </summary>
        /// <param name="request">批量删除请求</param>
        /// <returns>操作结果</returns>
        [HttpDelete("batch")]
        public async Task<ActionResult<ApiResponse<BatchOperationResultDto>>> BatchDeleteTasks(BatchDeleteRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid || !request.TaskIds.Any())
                {
                    return BadRequest(ApiResponseFactory.CreateFail<BatchOperationResultDto>("请求参数无效"));
                }

                var currentUserId = _currentUserService.GetCurrentUserId();
                var results = new List<BatchOperationItemResult>();

                foreach (var taskId in request.TaskIds)
                {
                    try
                    {
                        var result = await _taskService.DeleteTaskAsync(taskId, currentUserId);
                        results.Add(new BatchOperationItemResult
                        {
                            ItemId = taskId.ToString(),
                            Success = result.Success,
                            Message = result.Message
                        });
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "批量删除失败，TaskId: {TaskId}", taskId);
                        results.Add(new BatchOperationItemResult
                        {
                            ItemId = taskId.ToString(),
                            Success = false,
                            Message = "操作失败: " + ex.Message
                        });
                    }
                }

                var batchResult = new BatchOperationResultDto
                {
                    TotalCount = request.TaskIds.Count,
                    SuccessCount = results.Count(r => r.Success),
                    FailureCount = results.Count(r => !r.Success),
                    Results = results
                };

                return Ok(ApiResponseFactory.CreateSuccess(batchResult, "批量删除完成"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量删除任务时发生错误");
                return StatusCode(500, ApiResponseFactory.CreateFail<BatchOperationResultDto>("服务器内部错误"));
            }
        }

        #endregion

        #region 评论管理

        /// <summary>
        /// 获取任务评论列表
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>评论列表</returns>
        [HttpGet("{id:long}/comments")]
        public async Task<ActionResult<ApiResponse<List<CommentDto>>>> GetTaskComments(long id)
        {
            try
            {
                var result = await _taskService.GetTaskCommentsAsync(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务评论时发生错误，TaskId: {TaskId}", id);
                return StatusCode(500, ApiResponseFactory.CreateFail<List<CommentDto>>("服务器内部错误"));
            }
        }

        /// <summary>
        /// 添加任务评论
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="request">评论请求</param>
        /// <returns>添加的评论</returns>
        [HttpPost("{id:long}/comments")]
        public async Task<ActionResult<ApiResponse<CommentDto>>> AddComment(long id, AddCommentRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<CommentDto>("请求参数无效"));
                }

                var currentUserId = _currentUserService.GetCurrentUserId();
                var result = await _taskService.AddCommentAsync(id, request, currentUserId);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加任务评论时发生错误，TaskId: {TaskId}", id);
                return StatusCode(500, ApiResponseFactory.CreateFail<CommentDto>("服务器内部错误"));
            }
        }

        #endregion

        #region 附件管理

        /// <summary>
        /// 获取任务附件列表
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>附件列表</returns>
        [HttpGet("{id:long}/attachments")]
        public async Task<ActionResult<ApiResponse<List<AttachmentDto>>>> GetTaskAttachments(long id)
        {
            try
            {
                var result = await _taskService.GetTaskAttachmentsAsync(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务附件时发生错误，TaskId: {TaskId}", id);
                return StatusCode(500, ApiResponseFactory.CreateFail<List<AttachmentDto>>("服务器内部错误"));
            }
        }

        /// <summary>
        /// 上传任务附件
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="file">文件</param>
        /// <param name="description">描述</param>
        /// <returns>上传的附件</returns>
        [HttpPost("{id:long}/attachments")]
        public async Task<ActionResult<ApiResponse<AttachmentDto>>> AddAttachment(long id, IFormFile file, [FromForm] string description = "")
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<AttachmentDto>("请选择要上传的文件"));
                }

                // 文件大小限制（例如10MB）
                const int maxFileSize = 10 * 1024 * 1024;
                if (file.Length > maxFileSize)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<AttachmentDto>("文件大小不能超过10MB"));
                }

                var currentUserId = _currentUserService.GetCurrentUserId();
                
                // 读取文件内容
                using var memoryStream = new MemoryStream();
                await file.CopyToAsync(memoryStream);
                var fileContent = memoryStream.ToArray();

                var result = await _taskService.AddAttachmentAsync(id, file.FileName, fileContent, file.ContentType, description, currentUserId);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "上传任务附件时发生错误，TaskId: {TaskId}", id);
                return StatusCode(500, ApiResponseFactory.CreateFail<AttachmentDto>("服务器内部错误"));
            }
        }

        /// <summary>
        /// 删除附件
        /// </summary>
        /// <param name="attachmentId">附件ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("attachments/{attachmentId:long}")]
        public async Task<ActionResult<ApiResponse<bool>>> DeleteAttachment(long attachmentId)
        {
            try
            {
                var currentUserId = _currentUserService.GetCurrentUserId();
                var result = await _taskService.DeleteAttachmentAsync(attachmentId, currentUserId);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除附件时发生错误，AttachmentId: {AttachmentId}", attachmentId);
                return StatusCode(500, ApiResponseFactory.CreateFail<bool>("服务器内部错误"));
            }
        }

        #endregion

        #region 历史记录

        /// <summary>
        /// 获取任务历史记录
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>历史记录列表</returns>
        [HttpGet("{id:long}/history")]
        public async Task<ActionResult<ApiResponse<List<TaskHistoryDto>>>> GetTaskHistory(long id)
        {
            try
            {
                var result = await _taskService.GetTaskHistoryAsync(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务历史记录时发生错误，TaskId: {TaskId}", id);
                return StatusCode(500, ApiResponseFactory.CreateFail<List<TaskHistoryDto>>("服务器内部错误"));
            }
        }

        #endregion

        #region 统计和报表

        /// <summary>
        /// 获取任务统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        [HttpGet("statistics")]
        public async Task<ActionResult<ApiResponse<TaskStatisticsDto>>> GetTaskStatistics()
        {
            try
            {
                // 获取所有任务
                var allTasksResult = await _taskService.GetTasksAsync(new TaskQueryParametersDto
                {
                    PageNumber = 1,
                    PageSize = int.MaxValue
                });

                if (!allTasksResult.Success)
                {
                    return BadRequest(allTasksResult);
                }

                var tasks = allTasksResult.Data ?? new List<TaskDto>();
                var now = DateTime.UtcNow;

                var statistics = new TaskStatisticsDto
                {
                    TotalTasks = tasks.Count,
                    CompletedTasks = tasks.Count(t => t.Status == "Done"),
                    InProgressTasks = tasks.Count(t => t.Status == "InProgress"),
                    TodoTasks = tasks.Count(t => t.Status == "Todo"),
                    OverdueTasks = tasks.Count(t => t.IsOverdue),
                    TasksByPriority = new Dictionary<string, int>
                    {
                        { "High", tasks.Count(t => t.Priority == "High") },
                        { "Medium", tasks.Count(t => t.Priority == "Medium") },
                        { "Low", tasks.Count(t => t.Priority == "Low") }
                    },
                    TasksByStatus = new Dictionary<string, int>
                    {
                        { "Todo", tasks.Count(t => t.Status == "Todo") },
                        { "InProgress", tasks.Count(t => t.Status == "InProgress") },
                        { "Done", tasks.Count(t => t.Status == "Done") },
                        { "Cancelled", tasks.Count(t => t.Status == "Cancelled") }
                    },
                    UpcomingDeadlines = tasks
                        .Where(t => t.PlanEndDate.HasValue && t.Status != "Done" && t.PlanEndDate.Value <= now.AddDays(7))
                        .OrderBy(t => t.PlanEndDate)
                        .Take(10)
                        .ToList()
                };

                return Ok(ApiResponseFactory.CreateSuccess(statistics, "获取统计信息成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务统计信息时发生错误");
                return StatusCode(500, ApiResponseFactory.CreateFail<TaskStatisticsDto>("服务器内部错误"));
            }
        }

        /// <summary>
        /// 获取任务分析数据
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>分析数据</returns>
        [HttpGet("analytics")]
        public async Task<ActionResult<ApiResponse<TaskAnalyticsDto>>> GetTaskAnalytics([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var start = startDate ?? DateTime.UtcNow.AddDays(-30);
                var end = endDate ?? DateTime.UtcNow;

                var allTasksResult = await _taskService.GetTasksAsync(new TaskQueryParametersDto
                {
                    PageNumber = 1,
                    PageSize = int.MaxValue,
                    CreateStartDate = start,
                    CreateEndDate = end
                });

                if (!allTasksResult.Success)
                {
                    return BadRequest(allTasksResult);
                }

                var tasks = allTasksResult.Data ?? new List<TaskDto>();
                
                var analytics = new TaskAnalyticsDto
                {
                    Period = new AnalyticsPeriodDto
                    {
                        StartDate = start,
                        EndDate = end,
                        Days = (int)(end - start).TotalDays
                    },
                    KpiMetrics = new KpiMetricsDto
                    {
                        TotalTasks = tasks.Count(),
                        CompletedTasks = tasks.Count(t => t.Status == "Done"),
                        OverdueTasks = tasks.Count(t => t.IsOverdue),
                        CompletionRate = tasks.Count() > 0 ? (double)tasks.Count(t => t.Status == "Done") / tasks.Count() * 100 : 0
                    },
                    StatusDistribution = tasks.GroupBy(t => t.Status)
                        .ToDictionary(g => g.Key, g => g.Count()),
                    PriorityDistribution = tasks.GroupBy(t => t.Priority)
                        .ToDictionary(g => g.Key, g => g.Count()),
                    CompletionTrend = GetCompletionTrend(tasks, start, end),
                    TeamEfficiency = await GetTeamEfficiencyData(tasks),
                    Insights = GenerateInsights(tasks)
                };

                return Ok(ApiResponseFactory.CreateSuccess(analytics, "获取分析数据成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务分析数据时发生错误");
                return StatusCode(500, ApiResponseFactory.CreateFail<TaskAnalyticsDto>("服务器内部错误"));
            }
        }

        /// <summary>
        /// 获取团队效率数据
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>团队效率数据</returns>
        [HttpGet("team-efficiency")]
        public async Task<ActionResult<ApiResponse<List<TeamEfficiencyDto>>>> GetTeamEfficiency([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var start = startDate ?? DateTime.UtcNow.AddDays(-30);
                var end = endDate ?? DateTime.UtcNow;

                var allTasksResult = await _taskService.GetTasksAsync(new TaskQueryParametersDto
                {
                    PageNumber = 1,
                    PageSize = int.MaxValue,
                    CreateStartDate = start,
                    CreateEndDate = end
                });

                if (!allTasksResult.Success)
                {
                    return BadRequest(allTasksResult);
                }

                var tasks = allTasksResult.Data ?? new List<TaskDto>();
                var teamEfficiency = await GetTeamEfficiencyData(tasks);

                return Ok(ApiResponseFactory.CreateSuccess(teamEfficiency, "获取团队效率数据成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取团队效率数据时发生错误");
                return StatusCode(500, ApiResponseFactory.CreateFail<List<TeamEfficiencyDto>>("服务器内部错误"));
            }
        }

        /// <summary>
        /// 获取任务趋势数据
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>趋势数据</returns>
        [HttpGet("trends")]
        public async Task<ActionResult<ApiResponse<TaskTrendsDto>>> GetTaskTrends([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var start = startDate ?? DateTime.UtcNow.AddDays(-30);
                var end = endDate ?? DateTime.UtcNow;

                var allTasksResult = await _taskService.GetTasksAsync(new TaskQueryParametersDto
                {
                    PageNumber = 1,
                    PageSize = int.MaxValue,
                    CreateStartDate = start,
                    CreateEndDate = end
                });

                if (!allTasksResult.Success)
                {
                    return BadRequest(allTasksResult);
                }

                var tasks = allTasksResult.Data ?? new List<TaskDto>();
                
                var trends = new TaskTrendsDto
                {
                    Period = new AnalyticsPeriodDto
                    {
                        StartDate = start,
                        EndDate = end,
                        Days = (int)(end - start).TotalDays
                    },
                    CompletionTrend = GetCompletionTrend(tasks, start, end),
                    CreationTrend = GetCreationTrend(tasks, start, end),
                    OverdueTrend = GetOverdueTrend(tasks, start, end)
                };

                return Ok(ApiResponseFactory.CreateSuccess(trends, "获取趋势数据成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务趋势数据时发生错误");
                return StatusCode(500, ApiResponseFactory.CreateFail<TaskTrendsDto>("服务器内部错误"));
            }
        }

        #endregion

        #region 智能推荐和优化

        /// <summary>
        /// 获取智能任务分配建议
        /// </summary>
        /// <param name="request">智能分配请求参数</param>
        /// <returns>分配建议</returns>
        [HttpPost("smart-assignment")]
        public async Task<ActionResult<ApiResponse<SmartAssignmentDto>>> GetSmartAssignment(SmartAssignmentRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid || !request.TaskIds.Any())
                {
                    return BadRequest(ApiResponseFactory.CreateFail<SmartAssignmentDto>("请求参数无效"));
                }

                // 获取用户工作负载数据
                var workloadData = await GetUserWorkloads();
                
                // 获取任务详情
                var tasks = new List<TaskDto>();
                foreach (var taskId in request.TaskIds)
                {
                    var taskResult = await _taskService.GetTaskByIdAsync(taskId);
                    if (taskResult.Success && taskResult.Data != null)
                    {
                        tasks.Add(taskResult.Data);
                    }
                }

                // 生成智能分配建议
                var suggestions = GenerateAssignmentSuggestions(tasks, workloadData, request.Strategy);

                var result = new SmartAssignmentDto
                {
                    TaskCount = tasks.Count,
                    Strategy = request.Strategy,
                    Suggestions = suggestions,
                    WorkloadImpact = CalculateWorkloadImpact(suggestions, workloadData)
                };

                return Ok(ApiResponseFactory.CreateSuccess(result, "获取智能分配建议成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取智能分配建议时发生错误");
                return StatusCode(500, ApiResponseFactory.CreateFail<SmartAssignmentDto>("服务器内部错误"));
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取任务状态选项
        /// </summary>
        /// <returns>状态选项列表</returns>
        [HttpGet("status-options")]
        public ActionResult<ApiResponse<List<string>>> GetTaskStatusOptions()
        {
            try
            {
                var statuses = _taskService.GetTaskStatuses();
                return Ok(ApiResponseFactory.CreateSuccess(statuses, "获取状态选项成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务状态选项时发生错误");
                return StatusCode(500, ApiResponseFactory.CreateFail<List<string>>("服务器内部错误"));
            }
        }

        /// <summary>
        /// 获取任务优先级选项
        /// </summary>
        /// <returns>优先级选项列表</returns>
        [HttpGet("priority-options")]
        public ActionResult<ApiResponse<List<string>>> GetTaskPriorityOptions()
        {
            try
            {
                var priorities = _taskService.GetTaskPriorities();
                return Ok(ApiResponseFactory.CreateSuccess(priorities, "获取优先级选项成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务优先级选项时发生错误");
                return StatusCode(500, ApiResponseFactory.CreateFail<List<string>>("服务器内部错误"));
            }
        }

        /// <summary>
        /// 获取任务类型选项
        /// </summary>
        /// <returns>类型选项列表</returns>
        [HttpGet("type-options")]
        public ActionResult<ApiResponse<List<string>>> GetTaskTypeOptions()
        {
            try
            {
                var types = _taskService.GetTaskTypes();
                return Ok(ApiResponseFactory.CreateSuccess(types, "获取类型选项成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务类型选项时发生错误");
                return StatusCode(500, ApiResponseFactory.CreateFail<List<string>>("服务器内部错误"));
            }
        }

        /// <summary>
        /// 生成完成趋势数据
        /// </summary>
        private List<TrendDataPointDto> GetCompletionTrend(List<TaskDto> tasks, DateTime start, DateTime end)
        {
            var trends = new List<TrendDataPointDto>();
            var current = start.Date;
            
            while (current <= end.Date)
            {
                var completedCount = tasks.Count(t => 
                    t.Status == "Done" && 
                    t.CompletedAt.HasValue && 
                    t.CompletedAt.Value.Date == current);
                
                trends.Add(new TrendDataPointDto
                {
                    Date = current,
                    Value = completedCount,
                    Label = current.ToString("MM-dd")
                });
                
                current = current.AddDays(1);
            }
            
            return trends;
        }

        /// <summary>
        /// 生成创建趋势数据
        /// </summary>
        private List<TrendDataPointDto> GetCreationTrend(List<TaskDto> tasks, DateTime start, DateTime end)
        {
            var trends = new List<TrendDataPointDto>();
            var current = start.Date;
            
            while (current <= end.Date)
            {
                var createdCount = tasks.Count(t => t.CreatedAt.Date == current);
                
                trends.Add(new TrendDataPointDto
                {
                    Date = current,
                    Value = createdCount,
                    Label = current.ToString("MM-dd")
                });
                
                current = current.AddDays(1);
            }
            
            return trends;
        }

        /// <summary>
        /// 生成逾期趋势数据
        /// </summary>
        private List<TrendDataPointDto> GetOverdueTrend(List<TaskDto> tasks, DateTime start, DateTime end)
        {
            var trends = new List<TrendDataPointDto>();
            var current = start.Date;
            
            while (current <= end.Date)
            {
                var overdueCount = tasks.Count(t => 
                    t.PlanEndDate.HasValue && 
                    t.PlanEndDate.Value.Date == current && 
                    t.IsOverdue);
                
                trends.Add(new TrendDataPointDto
                {
                    Date = current,
                    Value = overdueCount,
                    Label = current.ToString("MM-dd")
                });
                
                current = current.AddDays(1);
            }
            
            return trends;
        }

        /// <summary>
        /// 获取团队效率数据
        /// </summary>
        private async Task<List<TeamEfficiencyDto>> GetTeamEfficiencyData(List<TaskDto> tasks)
        {
            // 模拟团队数据，实际应该从数据库获取
            var teams = new List<TeamEfficiencyDto>
            {
                new TeamEfficiencyDto { Id = 1, Name = "技术团队", Score = 95, CompletionRate = 92 },
                new TeamEfficiencyDto { Id = 2, Name = "运维团队", Score = 88, CompletionRate = 85 },
                new TeamEfficiencyDto { Id = 3, Name = "产品团队", Score = 82, CompletionRate = 78 },
                new TeamEfficiencyDto { Id = 4, Name = "市场团队", Score = 75, CompletionRate = 72 },
                new TeamEfficiencyDto { Id = 5, Name = "销售团队", Score = 68, CompletionRate = 65 }
            };

            return await Task.FromResult(teams);
        }

        /// <summary>
        /// 生成智能洞察
        /// </summary>
        private List<InsightDto> GenerateInsights(List<TaskDto> tasks)
        {
            var insights = new List<InsightDto>();
            
            // 逾期任务增加警告
            var overdueCount = tasks.Count(t => t.IsOverdue);
            if (overdueCount > 0)
            {
                insights.Add(new InsightDto
                {
                    Type = "warning",
                    Title = "逾期任务较多",
                    Description = $"当前有 {overdueCount} 个逾期任务，建议优化任务分配策略",
                    Priority = "high"
                });
            }

            // 完成率分析
            var completionRate = tasks.Count > 0 ? (double)tasks.Count(t => t.Status == "Done") / tasks.Count * 100 : 0;
            if (completionRate > 80)
            {
                insights.Add(new InsightDto
                {
                    Type = "success",
                    Title = "任务完成率良好",
                    Description = $"当前任务完成率为 {completionRate:F1}%，团队表现优秀",
                    Priority = "info"
                });
            }

            return insights;
        }

        /// <summary>
        /// 获取用户工作负载数据
        /// </summary>
        private async Task<List<UserWorkloadDto>> GetUserWorkloads()
        {
            // 模拟用户工作负载数据
            var workloads = new List<UserWorkloadDto>
            {
                new UserWorkloadDto { UserId = 1, UserName = "张三", TaskCount = 5, CompletionRate = 85 },
                new UserWorkloadDto { UserId = 2, UserName = "李四", TaskCount = 3, CompletionRate = 92 },
                new UserWorkloadDto { UserId = 3, UserName = "王五", TaskCount = 7, CompletionRate = 78 }
            };

            return await Task.FromResult(workloads);
        }

        /// <summary>
        /// 生成智能分配建议
        /// </summary>
        private List<AssignmentSuggestionDto> GenerateAssignmentSuggestions(
            List<TaskDto> tasks, 
            List<UserWorkloadDto> workloads, 
            string strategy)
        {
            var suggestions = new List<AssignmentSuggestionDto>();

            switch (strategy?.ToLower() ?? "default")
            {
                case "balanced":
                    // 负载均衡策略
                    var sortedByWorkload = workloads.OrderBy(w => w.TaskCount).ToList();
                    for (int i = 0; i < tasks.Count; i++)
                    {
                        var user = sortedByWorkload[i % sortedByWorkload.Count];
                        suggestions.Add(new AssignmentSuggestionDto
                        {
                            TaskId = tasks[i].TaskId,
                            UserId = user.UserId,
                            UserName = user.UserName,
                            Reason = "负载均衡分配",
                            Confidence = 85
                        });
                    }
                    break;

                case "performance":
                    // 按性能分配策略
                    var sortedByPerformance = workloads.OrderByDescending(w => w.CompletionRate).ToList();
                    foreach (var task in tasks)
                    {
                        var bestUser = sortedByPerformance.First();
                        suggestions.Add(new AssignmentSuggestionDto
                        {
                            TaskId = task.TaskId,
                            UserId = bestUser.UserId,
                            UserName = bestUser.UserName,
                            Reason = "基于完成率的最优分配",
                            Confidence = 90
                        });
                    }
                    break;

                default:
                    // 默认随机分配
                    var random = new Random();
                    foreach (var task in tasks)
                    {
                        var randomUser = workloads[random.Next(workloads.Count)];
                        suggestions.Add(new AssignmentSuggestionDto
                        {
                            TaskId = task.TaskId,
                            UserId = randomUser.UserId,
                            UserName = randomUser.UserName,
                            Reason = "随机分配",
                            Confidence = 60
                        });
                    }
                    break;
            }

            return suggestions;
        }

        /// <summary>
        /// 计算工作负载影响
        /// </summary>
        private WorkloadImpactDto CalculateWorkloadImpact(
            List<AssignmentSuggestionDto> suggestions, 
            List<UserWorkloadDto> currentWorkloads)
        {
            var impact = new WorkloadImpactDto
            {
                AffectedUsers = suggestions.Select(s => s.UserId).Distinct().Count(),
                MaxTaskIncrease = suggestions.GroupBy(s => s.UserId)
                    .Max(g => g.Count()),
                RecommendedRebalancing = suggestions.Count > 5
            };

            return impact;
        }

        #endregion
    }

    #region 请求DTO类

    /// <summary>
    /// 批量状态更新请求
    /// </summary>
    public class BatchUpdateStatusRequestDto
    {
        [Required]
        public List<long> TaskIds { get; set; } = new();
        
        [Required]
        public string Status { get; set; } = string.Empty;
        
        public string Remarks { get; set; } = string.Empty;
    }

    /// <summary>
    /// 批量分配请求
    /// </summary>
    public class BatchAssignRequestDto
    {
        [Required]
        public List<long> TaskIds { get; set; } = new();
        
        [Required]
        public int AssigneeUserId { get; set; }
        
        public string Remarks { get; set; } = string.Empty;
    }

    /// <summary>
    /// 批量删除请求
    /// </summary>
    public class BatchDeleteRequestDto
    {
        [Required]
        public List<long> TaskIds { get; set; } = new();
    }

    /// <summary>
    /// 状态更新请求
    /// </summary>
    public class UpdateTaskStatusRequestDto
    {
        [Required]
        public string Status { get; set; } = string.Empty;
        
        public string Remarks { get; set; } = string.Empty;
    }

    /// <summary>
    /// 进度更新请求
    /// </summary>
    public class UpdateTaskProgressRequestDto
    {
        [Required]
        [Range(0, 100)]
        public int Progress { get; set; }
        
        public string Remarks { get; set; } = string.Empty;
    }

    /// <summary>
    /// 分配请求
    /// </summary>
    public class AssignTaskRequestDto
    {
        [Required]
        public int AssigneeUserId { get; set; }
        
        public string Remarks { get; set; } = string.Empty;
    }

    /// <summary>
    /// 完成请求
    /// </summary>
    public class CompleteTaskRequestDto
    {
        public string Remarks { get; set; } = string.Empty;
    }

    /// <summary>
    /// 批量操作结果
    /// </summary>
    public class BatchOperationResultDto
    {
        public int TotalCount { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public List<BatchOperationItemResult> Results { get; set; } = new();
    }

    /// <summary>
    /// 批量操作项结果
    /// </summary>
    public class BatchOperationItemResult
    {
        public string ItemId { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }


    /// <summary>
    /// 任务分析数据
    /// </summary>
    public class TaskAnalyticsDto
    {
        public AnalyticsPeriodDto Period { get; set; } = new();
        public KpiMetricsDto KpiMetrics { get; set; } = new();
        public Dictionary<string, int> StatusDistribution { get; set; } = new();
        public Dictionary<string, int> PriorityDistribution { get; set; } = new();
        public List<TrendDataPointDto> CompletionTrend { get; set; } = new();
        public List<TeamEfficiencyDto> TeamEfficiency { get; set; } = new();
        public List<InsightDto> Insights { get; set; } = new();
    }

    /// <summary>
    /// 分析周期信息
    /// </summary>
    public class AnalyticsPeriodDto
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int Days { get; set; }
    }

    /// <summary>
    /// KPI指标数据
    /// </summary>
    public class KpiMetricsDto
    {
        public int TotalTasks { get; set; }
        public int CompletedTasks { get; set; }
        public int OverdueTasks { get; set; }
        public double CompletionRate { get; set; }
    }

    /// <summary>
    /// 趋势数据点
    /// </summary>
    public class TrendDataPointDto
    {
        public DateTime Date { get; set; }
        public int Value { get; set; }
        public string Label { get; set; } = string.Empty;
    }

    /// <summary>
    /// 团队效率数据
    /// </summary>
    public class TeamEfficiencyDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public int Score { get; set; }
        public double CompletionRate { get; set; }
    }

    /// <summary>
    /// 智能洞察数据
    /// </summary>
    public class InsightDto
    {
        public string Type { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
    }

    /// <summary>
    /// 任务趋势数据
    /// </summary>
    public class TaskTrendsDto
    {
        public AnalyticsPeriodDto Period { get; set; } = new();
        public List<TrendDataPointDto> CompletionTrend { get; set; } = new();
        public List<TrendDataPointDto> CreationTrend { get; set; } = new();
        public List<TrendDataPointDto> OverdueTrend { get; set; } = new();
    }

    /// <summary>
    /// 智能分配请求
    /// </summary>
    public class SmartAssignmentRequestDto
    {
        [Required]
        public List<long> TaskIds { get; set; } = new();
        
        public string Strategy { get; set; } = "balanced"; // balanced, performance, random
    }

    /// <summary>
    /// 智能分配结果
    /// </summary>
    public class SmartAssignmentDto
    {
        public int TaskCount { get; set; }
        public string Strategy { get; set; } = string.Empty;
        public List<AssignmentSuggestionDto> Suggestions { get; set; } = new();
        public WorkloadImpactDto WorkloadImpact { get; set; } = new();
    }

    /// <summary>
    /// 分配建议
    /// </summary>
    public class AssignmentSuggestionDto
    {
        public long TaskId { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
        public int Confidence { get; set; } // 置信度 0-100
    }

    /// <summary>
    /// 用户工作负载数据
    /// </summary>
    public class UserWorkloadDto
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public int TaskCount { get; set; }
        public double CompletionRate { get; set; }
    }

    /// <summary>
    /// 工作负载影响评估
    /// </summary>
    public class WorkloadImpactDto
    {
        public int AffectedUsers { get; set; }
        public int MaxTaskIncrease { get; set; }
        public bool RecommendedRebalancing { get; set; }
    }

    #endregion
}