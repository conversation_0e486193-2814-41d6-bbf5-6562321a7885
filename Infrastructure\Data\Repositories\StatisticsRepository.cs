using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Application.Features.Statistics.Dtos;
using ItAssetsSystem.Application.Features.Statistics.Queries;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace ItAssetsSystem.Infrastructure.Data.Repositories
{
    /// <summary>
    /// Implementation of statistics repository using Entity Framework
    /// </summary>
    public class StatisticsRepository : IStatisticsRepository
    {
        private readonly AppDbContext _context;
        private readonly ILogger<StatisticsRepository> _logger;

        public StatisticsRepository(AppDbContext context, ILogger<StatisticsRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Executes a dynamic statistics query with configurable dimensions and metrics
        /// </summary>
        public async Task<DynamicStatisticsResultDto> GetDynamicStatisticsAsync(
            DynamicStatisticsQuery query, 
            CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var queryRequest = query.QueryRequest;

            try
            {
                _logger.LogInformation("开始执行动态统计查询: 维度={Dimension}, 度量={Metric}", 
                    queryRequest.Dimension, queryRequest.Metric);

                // Build base query for assets
                var baseQuery = _context.Assets.AsNoTracking();

                // Apply filters
                baseQuery = ApplyFilters(baseQuery, queryRequest);

                // Apply date range filter
                if (queryRequest.DateRange != null)
                {
                    baseQuery = ApplyDateRangeFilter(baseQuery, queryRequest.DateRange);
                }

                // Execute aggregation based on dimension and metric
                var aggregatedData = await ExecuteAggregationAsync(baseQuery, queryRequest, cancellationToken);

                // Get detailed data for table display
                var detailedData = await GetDetailedDataAsync(baseQuery, queryRequest, cancellationToken);

                // Build result
                var result = new DynamicStatisticsResultDto
                {
                    Summary = BuildSummary(aggregatedData),
                    AggregatedData = aggregatedData,
                    DetailedData = detailedData,
                    ExecutionMetadata = new ExecutionMetadataDto
                    {
                        ExecutionTimeMs = stopwatch.ElapsedMilliseconds,
                        DataSource = "Assets",
                        FromCache = false,
                        QuerySignature = $"{queryRequest.Dimension}_{queryRequest.Metric}"
                    }
                };

                // Apply pagination if specified
                if (queryRequest.Pagination != null)
                {
                    // 获取详细数据的总数用于分页
                    var totalDetailedCount = await baseQuery.CountAsync(cancellationToken);

                    result.Pagination = new PaginationMetadataDto
                    {
                        CurrentPage = queryRequest.Pagination.Page,
                        PageSize = queryRequest.Pagination.Size,
                        TotalCount = totalDetailedCount,
                        TotalPages = (int)Math.Ceiling((double)totalDetailedCount / queryRequest.Pagination.Size),
                        HasNext = queryRequest.Pagination.Page * queryRequest.Pagination.Size < totalDetailedCount,
                        HasPrevious = queryRequest.Pagination.Page > 1
                    };

                    // 聚合数据不需要分页，显示所有分组
                    // 详细数据已经在 GetDetailedDataAsync 中应用了分页
                }

                _logger.LogInformation("动态统计查询完成: 返回 {Count} 条聚合记录，耗时 {Duration}ms", 
                    result.AggregatedData.Count, stopwatch.ElapsedMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行动态统计查询时发生错误");
                throw;
            }
            finally
            {
                stopwatch.Stop();
            }
        }

        /// <summary>
        /// Gets asset snapshot data for historical trends
        /// </summary>
        public async Task<IEnumerable<AssetSnapshotDto>> GetAssetSnapshotsAsync(
            DateTime? startDate = null,
            DateTime? endDate = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                // 暂时返回空列表，等AssetSnapshot实体完全配置好后再实现
                await Task.CompletedTask;
                return new List<AssetSnapshotDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取资产快照数据时发生错误");
                throw;
            }
        }

        private IQueryable<Models.Entities.Asset> ApplyFilters(IQueryable<Models.Entities.Asset> query, DynamicStatisticsQueryDto queryRequest)
        {
            foreach (var filter in queryRequest.Filters)
            {
                switch (filter.Key.ToLower())
                {
                    case "status":
                        if (int.TryParse(filter.Value?.ToString(), out int statusValue))
                            query = query.Where(a => a.Status == statusValue);
                        break;
                    case "assettypeid":
                        if (int.TryParse(filter.Value?.ToString(), out int assetTypeId))
                            query = query.Where(a => a.AssetTypeId == assetTypeId);
                        break;
                    case "departmentid":
                        if (int.TryParse(filter.Value?.ToString(), out int departmentId))
                            query = query.Where(a => a.DepartmentId == departmentId);
                        break;
                    case "locationid":
                        if (int.TryParse(filter.Value?.ToString(), out int locationId))
                            query = query.Where(a => a.LocationId == locationId);
                        break;
                }
            }

            return query;
        }

        private IQueryable<Models.Entities.Asset> ApplyDateRangeFilter(IQueryable<Models.Entities.Asset> query, DateRangeFilterDto dateRange)
        {
            if (dateRange.StartDate.HasValue)
            {
                switch (dateRange.Field?.ToLower())
                {
                    case "createdat":
                    default:
                        query = query.Where(a => a.CreatedAt >= dateRange.StartDate.Value);
                        break;
                    case "updatedat":
                        query = query.Where(a => a.UpdatedAt >= dateRange.StartDate.Value);
                        break;
                }
            }

            if (dateRange.EndDate.HasValue)
            {
                switch (dateRange.Field?.ToLower())
                {
                    case "createdat":
                    default:
                        query = query.Where(a => a.CreatedAt <= dateRange.EndDate.Value);
                        break;
                    case "updatedat":
                        query = query.Where(a => a.UpdatedAt <= dateRange.EndDate.Value);
                        break;
                }
            }

            return query;
        }

        private async Task<List<AggregatedDataItemDto>> ExecuteAggregationAsync(
            IQueryable<Models.Entities.Asset> baseQuery, 
            DynamicStatisticsQueryDto queryRequest, 
            CancellationToken cancellationToken)
        {
            var result = new List<AggregatedDataItemDto>();

            switch (queryRequest.Dimension.ToLower())
            {
                case "department":
                    // 参考AssetStatisticsService.cs的部门统计逻辑：使用Path第二个值对应位置的部门
                    var assetsWithLocation = await baseQuery
                        .Include(a => a.Location)
                        .Where(a => a.Location != null && !string.IsNullOrEmpty(a.Location.Path))
                        .Select(a => new
                        {
                            a.Id,
                            a.Price,
                            LocationPath = a.Location.Path
                        })
                        .ToListAsync(cancellationToken);

                    // 获取所有位置的部门信息（用于查找Path中第二个值对应的位置的部门）
                    var allLocations = await _context.Locations
                        .Select(l => new { l.Id, l.DefaultDepartmentId })
                        .ToDictionaryAsync(l => l.Id, l => l.DefaultDepartmentId, cancellationToken);

                    // 获取所有部门信息
                    var allDepartments = await _context.Departments
                        .ToDictionaryAsync(d => d.Id, d => d.Name, cancellationToken);

                    // 按部门分组资产（统一使用Path第二个值对应位置的部门）
                    var assetsByDepartment = new Dictionary<int, List<dynamic>>();

                    foreach (var asset in assetsWithLocation)
                    {
                        int? departmentId = null;

                        // 统一逻辑：使用Path中第二个值对应位置的部门
                        var pathParts = asset.LocationPath.Split(',');
                        if (pathParts.Length >= 2 && int.TryParse(pathParts[1], out int secondLocationId))
                        {
                            if (allLocations.ContainsKey(secondLocationId) && allLocations[secondLocationId].HasValue)
                            {
                                departmentId = allLocations[secondLocationId].Value;
                            }
                        }

                        // 如果找到了有效的部门ID，添加到分组中
                        if (departmentId.HasValue && allDepartments.ContainsKey(departmentId.Value))
                        {
                            if (!assetsByDepartment.ContainsKey(departmentId.Value))
                            {
                                assetsByDepartment[departmentId.Value] = new List<dynamic>();
                            }
                            assetsByDepartment[departmentId.Value].Add(asset);
                        }
                    }

                    // 构建统计结果
                    result = assetsByDepartment.Select(kvp =>
                    {
                        var departmentId = kvp.Key;
                        var assets = kvp.Value;
                        var departmentName = allDepartments.ContainsKey(departmentId) ? allDepartments[departmentId] : $"部门{departmentId}";
                        var totalValue = assets.Sum(a => a.Price ?? 0);

                        return new AggregatedDataItemDto
                        {
                            Label = "部门",
                            Value = departmentName,
                            MetricValue = GetMetricValue(queryRequest.Metric, assets.Count, totalValue),
                            Percentage = 0 // Will be calculated later
                        };
                    }).ToList();
                    break;

                case "assettype":
                    var typeGroups = await baseQuery
                        .Include(a => a.AssetType)
                        .GroupBy(a => new { a.AssetTypeId, a.AssetType.Name })
                        .Select(g => new { g.Key.Name, Count = g.Count(), TotalValue = g.Sum(a => a.Price ?? 0) })
                        .ToListAsync(cancellationToken);

                    result = typeGroups.Select(g => new AggregatedDataItemDto
                    {
                        Label = "资产类型",
                        Value = g.Name ?? "未分类",
                        MetricValue = GetMetricValue(queryRequest.Metric, g.Count, g.TotalValue),
                        Percentage = 0 // Will be calculated later
                    }).ToList();
                    break;

                case "status":
                    var statusGroups = await baseQuery
                        .GroupBy(a => a.Status)
                        .Select(g => new { Status = g.Key, Count = g.Count(), TotalValue = g.Sum(a => a.Price ?? 0) })
                        .ToListAsync(cancellationToken);

                    result = statusGroups.Select(g => new AggregatedDataItemDto
                    {
                        Label = "状态",
                        Value = GetStatusName(g.Status),
                        MetricValue = GetMetricValue(queryRequest.Metric, g.Count, g.TotalValue),
                        Percentage = 0 // Will be calculated later
                    }).ToList();
                    break;

                case "location":
                case "region":
                    // 参考AssetStatisticsService.cs的区域统计逻辑：按Path第四个值分组统计
                    var assetsWithLocationPath = await baseQuery
                        .Include(a => a.Location)
                        .Where(a => a.Location != null && !string.IsNullOrEmpty(a.Location.Path))
                        .Select(a => new
                        {
                            a.Id,
                            a.Price,
                            LocationPath = a.Location.Path
                        })
                        .ToListAsync(cancellationToken);

                    // 按Path第四个值分组统计
                    var assetsByRegion = new Dictionary<int, List<dynamic>>();

                    foreach (var asset in assetsWithLocationPath)
                    {
                        var pathParts = asset.LocationPath.Split(',');
                        if (pathParts.Length >= 4 && int.TryParse(pathParts[3], out int regionId))
                        {
                            if (!assetsByRegion.ContainsKey(regionId))
                            {
                                assetsByRegion[regionId] = new List<dynamic>();
                            }
                            assetsByRegion[regionId].Add(asset);
                        }
                    }

                    // 获取这些区域ID对应的位置信息
                    var regionIds = assetsByRegion.Keys.ToList();
                    var regionInfos = await _context.Locations
                        .Where(l => regionIds.Contains(l.Id))
                        .ToDictionaryAsync(l => l.Id, l => l.Name, cancellationToken);

                    // 构建区域统计结果
                    result = assetsByRegion.Select(kvp =>
                    {
                        var regionId = kvp.Key;
                        var assets = kvp.Value;
                        var regionName = regionInfos.ContainsKey(regionId) ? regionInfos[regionId] : $"位置{regionId}";
                        var totalValue = assets.Sum(a => a.Price ?? 0);

                        return new AggregatedDataItemDto
                        {
                            Label = "区域",
                            Value = regionName,
                            MetricValue = GetMetricValue(queryRequest.Metric, assets.Count, totalValue),
                            Percentage = 0 // Will be calculated later
                        };
                    }).ToList();
                    break;

                default:
                    throw new ArgumentException($"不支持的维度: {queryRequest.Dimension}");
            }

            // Calculate percentages
            var total = result.Sum(r => r.MetricValue);
            if (total > 0)
            {
                foreach (var item in result)
                {
                    item.Percentage = Math.Round((item.MetricValue / total) * 100, 2);
                }
            }

            // Apply ordering
            if (!string.IsNullOrEmpty(queryRequest.OrderBy))
            {
                result = queryRequest.OrderDirection?.ToLower() == "asc"
                    ? result.OrderBy(r => r.MetricValue).ToList()
                    : result.OrderByDescending(r => r.MetricValue).ToList();
            }

            return result;
        }

        private async Task<List<Dictionary<string, object>>> GetDetailedDataAsync(
            IQueryable<Models.Entities.Asset> baseQuery,
            DynamicStatisticsQueryDto queryRequest,
            CancellationToken cancellationToken)
        {
            // Apply pagination to detailed data
            var skip = ((queryRequest.Pagination?.Page ?? 1) - 1) * (queryRequest.Pagination?.Size ?? 20);
            var take = queryRequest.Pagination?.Size ?? 20;

            // 1. 获取详细数据的中间结果，包含资产实体和位置路径
            var intermediateData = await baseQuery
                .Include(a => a.AssetType)
                .Include(a => a.Location)
                .Skip(skip)
                .Take(take)
                .Select(a => new
                {
                    a.Id,
                    AssetCode = a.AssetCode ?? $"A{a.Id:D6}",
                    AssetName = a.Name ?? "未命名资产",
                    AssetType = a.AssetType.Name ?? "未分类",
                    LocationName = a.Location.Name ?? "未知位置",
                    LocationPath = a.Location.Path,
                    Status = a.Status,
                    Price = a.Price ?? 0,
                    CreatedAt = a.CreatedAt
                })
                .ToListAsync(cancellationToken);

            // 2. 从路径中解析出第三个值作为locationId
            var locationIds = intermediateData
                .Select(item => {
                    var pathParts = item.LocationPath?.Split(',');
                    if (pathParts?.Length > 2 && int.TryParse(pathParts[2], out int locId))
                    {
                        return (int?)locId;
                    }
                    return null;
                })
                .Where(id => id.HasValue)
                .Distinct()
                .ToList();

            // 3. 一次性查询所有相关的位置、部门和负责人信息
            var locationDepartmentMap = await _context.Locations
                .Where(loc => locationIds.Contains(loc.Id) && loc.DefaultDepartmentId.HasValue)
                .Select(loc => new {
                    LocationId = loc.Id,
                    DepartmentId = loc.DefaultDepartmentId.Value,
                    DepartmentName = loc.Department.Name
                })
                .ToDictionaryAsync(x => x.LocationId, x => x, cancellationToken);

            // 获取部门管理人员信息
            var departmentIds = locationDepartmentMap.Values.Select(x => x.DepartmentId).Distinct().ToList();
            var departmentManagerMap = await _context.Departments
                .Where(d => departmentIds.Contains(d.Id) && d.ManagerId.HasValue)
                .Select(d => new {
                    DepartmentId = d.Id,
                    ManagerId = d.ManagerId.Value
                })
                .ToDictionaryAsync(x => x.DepartmentId, x => x.ManagerId, cancellationToken);

            // 获取管理人员姓名
            var managerIds = departmentManagerMap.Values.Distinct().ToList();
            var managerMap = await _context.Personnel
                .Where(p => managerIds.Contains(p.Id))
                .ToDictionaryAsync(p => p.Id, p => p.Name, cancellationToken);

            // 4. 构建最终的详细数据列表
            return intermediateData.Select(a => {
                var departmentName = "未分配部门";
                var managerName = "无负责人";

                // 从位置路径中解析部门信息
                var pathParts = a.LocationPath?.Split(',');
                if (pathParts?.Length > 2 && int.TryParse(pathParts[2], out int locationId))
                {
                    if (locationDepartmentMap.TryGetValue(locationId, out var deptInfo))
                    {
                        departmentName = deptInfo.DepartmentName;
                        
                        // 查找部门负责人
                        if (departmentManagerMap.TryGetValue(deptInfo.DepartmentId, out var managerId) &&
                            managerMap.TryGetValue(managerId, out var mgrName))
                        {
                            managerName = mgrName;
                        }
                    }
                }

                // 统一使用小写的键名(camelCase)，与前端保持一致
                return new Dictionary<string, object>
                {
                    { "id", a.Id },
                    { "assetCode", a.AssetCode },
                    { "assetName", a.AssetName },
                    { "assetType", a.AssetType },
                    { "department", departmentName },
                    { "location", a.LocationName },
                    { "status", GetStatusName(a.Status) },
                    { "price", a.Price },
                    { "createdAt", a.CreatedAt },
                    { "managerName", managerName } // 添加负责人信息
                };
            }).ToList();
        }

        private string GetAssetDepartmentFromPath(
            string locationPath,
            Dictionary<int, int?> allLocations,
            Dictionary<int, dynamic> allDepartments)
        {
            if (string.IsNullOrEmpty(locationPath))
            {
                return "未分配部门";
            }

            // 使用Path中第三个值对应的位置的部门
            var pathParts = locationPath.Split(',');
            if (pathParts.Length >= 3 && int.TryParse(pathParts[2], out int thirdLocationId))
            {
                if (allLocations.ContainsKey(thirdLocationId) && allLocations[thirdLocationId].HasValue)
                {
                    var departmentId = allLocations[thirdLocationId].Value;
                    if (allDepartments.ContainsKey(departmentId))
                    {
                        return allDepartments[departmentId].Name;
                    }
                }
            }

            return "未分配部门";
        }

        private string GetAssetDepartmentName(Models.Entities.Asset asset)
        {
            // 优先使用直接关联的部门
            if (asset.Department != null)
            {
                return asset.Department.Name;
            }

            // 如果没有直接部门，通过位置路径推断
            if (asset.Location != null && !string.IsNullOrEmpty(asset.Location.Path))
            {
                var pathParts = asset.Location.Path.Split(',');
                if (pathParts.Length >= 2 && int.TryParse(pathParts[1], out int secondLocationId))
                {
                    var location = _context.Locations
                        .Include(l => l.Department)
                        .FirstOrDefault(l => l.Id == secondLocationId);

                    if (location?.Department != null)
                    {
                        return location.Department.Name;
                    }
                }
            }

            return "未分配部门";
        }

        private decimal GetMetricValue(string metric, int count, decimal totalValue)
        {
            return metric?.ToLower() switch
            {
                "count" => count,
                "totalvalue" => totalValue,
                "averagevalue" => count > 0 ? totalValue / count : 0,
                _ => count
            };
        }

        private string GetStatusName(int status)
        {
            return status switch
            {
                1 => "在用",
                2 => "闲置",
                3 => "维修中",
                4 => "报废",
                _ => $"状态{status}"
            };
        }

        private StatisticsSummaryDto BuildSummary(List<AggregatedDataItemDto> aggregatedData)
        {
            return new StatisticsSummaryDto
            {
                TotalRecords = aggregatedData.Count,
                TotalValue = aggregatedData.Sum(a => a.MetricValue),
                AverageValue = aggregatedData.Count > 0 ? aggregatedData.Average(a => a.MetricValue) : 0,
                KeyMetrics = new Dictionary<string, object>
                {
                    { "max_value", aggregatedData.Count > 0 ? aggregatedData.Max(a => a.MetricValue) : 0 },
                    { "min_value", aggregatedData.Count > 0 ? aggregatedData.Min(a => a.MetricValue) : 0 }
                }
            };
        }


    }
}